#ifndef _MQTT_MGR_H
#define _MQTT_MGR_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

#include <mqttclient.h>
#include "mqtt_utils.h"
#include "data_type.h"

#define TIME_STMP_MIN   946656000       // 2000-01-01 00:00:00 的时间戳
#define TIME_STMP_MAX   4102416000      // 2100-01-01 00:00:00 的时间戳

#define WILL_DATA_LEN   72
#define OPT_UNSUB       0
#define OPT_SUB         1
#define MQTT_DETECT_COUNT     500     // 大约60s
#define MQTT_RECONN_COUNT     2000
#define MQTT_RESEND_COUNT     3        // 重连三次后变成1小时后再连
#define MQTT_RESEND_THRESH    30       // 大概1个小时


typedef struct periodic_pub {
    const char* topic_template;         ///<  发布主题模板
    void (*send_data)(const char*);     ///<  发送数据函数
} periodic_pub_t;

typedef struct passive_cmd {
    const char* topic_template;         ///<  接收主题模板
    char is_subscribed[MAX_CLIENT];                  ///<  是否已订阅
    int (*handler)(void* client, message_data_t* msg);    ///<  主题回调函数
} passive_cmd_t;

typedef struct
{
    char* config_name;         // 配置参数out_sid
    unsigned short para_sid;   // 配置参数sid
} config_name_tab_t;

typedef struct
{
    str16 site_id;
    str32 server_ip;
    unsigned short server_port;
} config_value_t;
// 临时保存配置信息

void handle_cert_chg_msg(_rt_msg_t curr_msg);
void init_config_para(void);
int is_need_close_conn(void);
void close_client(int which);
int handle_online(void* client);
void manage_connect(void);
int set_process_site_id(int which, char* site_id);
int topic_check(mqtt_client_t* client, char* topic_filter);
void sub_unsub_topic(int which_client, int opt);
int is_config_change(int which);
int handle_config(void* client, message_data_t* msg);
int handle_syn_time(void* client, message_data_t* msg);
#ifdef __cplusplus
}
#endif  //  __cplusplus
#endif //_MQTT_MGR_H