#include "mqtt_update.h"
#include "mqtt_utils.h"
#include "webclient.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "partition_def.h"
#include "update_manage.h"
#include "utils_time.h"
#include "utils_data_type_conversion.h"
#include "msg_id.h"
#include "msg.h"
#include "north_parallel_modbus.h"
#include "ee_public_info.h"
#include "utils_rtthread_security_func.h"
#include "delay_update.h"
#include "his_record.h"
#include <ctype.h>
#include "north_update_utils.h"


static rt_timer_t g_delay_update_info_timer = NULL;
time_t g_update_start_time = 0;
int g_update_file_type = 0;
unsigned char  g_real_update_status[PV_NUM] = {0};
char g_update_file_url[FILE_PATH_LEN] = {0};
update_file_info_t g_file_info[3] = {{0}};
int g_opt = 0;
unsigned short g_real_addr_info = 0;
int g_which_tls = 0;
unsigned char g_update_start_flag = FALSE;

static crt_file_info_t cert_files[FILE_COUNT] = {
    {"ca.pem", MQTT_CA_FILE, DEF_TLS},
    {"client.key", MQTT_CLIENT_KEY_FILE, DEF_TLS},
    {"client.pem", MQTT_CLIENT_FILE, DEF_TLS}
};

static crt_file_info_t cert_other_files[FILE_COUNT] = {
    {"ca_other.pem", MQTT_CA_OTHER_FILE, OTH_TLS},
    {"client_other.key", MQTT_CLIENT_KEY_OTHER_FILE, OTH_TLS},
    {"client_other.pem", MQTT_CLIENT_OTHER_FILE, OTH_TLS}
};

int judge_power_file_version(unsigned short* addr_info, int idx)
{
    int addr = 1;
    ms_ver_t* soft_ver = NULL;
    char* file_version[3] = {NULL, NULL, NULL};
    
    while(*addr_info != 0 && addr <= PV_NUM)
    {
        soft_ver = get_soft_ver(addr);
        file_version[CPLD_INDX] = soft_ver->cpld_ver;
        file_version[AUXI_INDX] = soft_ver->auxc_ver;
        file_version[MASTER_INDX] = soft_ver->mstc_ver;
        if(rt_strcmp(file_version[idx], g_file_info[idx].file_version) == 0)
        {   
            *addr_info &= ~(1 << addr);
        }
        addr++;
    }
    return g_file_info[idx].file_type;
}

int judge_moniter_file_version(unsigned short* addr_info, int idx)
{
    int addr = 1;
    ms_ver_t* soft_ver = NULL;

    while(*addr_info != 0 && addr <= PV_NUM)
    {
        soft_ver = get_soft_ver(addr);
        if(rt_strcmp(soft_ver->mnte_ver, g_file_info[idx].file_version) == 0)
        {
            *addr_info &= ~(1 << addr);
        }
        addr++;
    }
    return MONITER_FILE;
}

int mqtt_download_file(char* url, char* file_name, char* file_path, unsigned int* file_size, unsigned int* file_crc, int which_tls)
{
    static char s_file_path_tmp[FILE_PATH_LEN] = {0};
    rt_memset(s_file_path_tmp, 0x00, sizeof(s_file_path_tmp));
    rt_snprintf(s_file_path_tmp, FILE_PATH_LEN - 1, "%s/%s", url, file_name);
    return webclient_get_file(which_tls, s_file_path_tmp, file_path, g_opt, file_size, file_crc);
}

int judge_download_file(unsigned short* addr_info,unsigned short real_addr_info)
{
    int update_type = g_file_info[0].file_type;
    int power_indx = 0;
    //将主机的软件版本更新
    set_master_soft_ver();
    if(g_file_info[0].file_type == MONITER_FILE)
    {
       *addr_info = real_addr_info;
       update_type = judge_moniter_file_version(addr_info, 0);
    }
    else if (g_file_info[0].file_type <= CPLD_FILE)
    {
        for(power_indx = CPLD_INDX; power_indx >= 0; power_indx--)
        {
            *addr_info = real_addr_info;
            update_type = judge_power_file_version(addr_info, power_indx);
            RETURN_VAL_IF_FAIL(*addr_info == 0, update_type);
        }
    }
    return update_type;
}


static int download_files(const crt_file_info_t* files, char* url, unsigned int* file_size, unsigned int* file_crc)
{   
    int ret = FAILURE;
    for (int i = 0; i < FILE_COUNT; ++i) {
        ret = mqtt_download_file(url, files[i].name, files[i].file_name, file_size, file_crc, files[i].flag);
        RETURN_VAL_IF_FAIL(ret >= 0, FAILURE);
    }
    return SUCCESSFUL;
}

int download_file(char* url, int file_type, unsigned int* file_size, unsigned int* file_crc, int which_tls)
{
    int ret = FAILURE;
    char update_file_name[][FILE_NAME_LEN] = {
                                                            {"pv_app.bin"},
                                                            {"PV_INVERTER_MASTER.bin"},
                                                            {"PV_INVERTER_SLAVE.bin"},
                                                            {"PV_INVERTER_CPLD.bin"},
                                                        };
    char which_tls_msg = which_tls;
    update_app_info_t update_info = {0};
    handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&update_info, sizeof(update_info), MQTT_UPDATE_INFO_OFFSET);
    rt_memset_s(update_info.is_enter_update, 10, 0, 10);
    handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&update_info, sizeof(update_info), MQTT_UPDATE_INFO_OFFSET);

    RETURN_VAL_IF_FAIL(url != NULL, FAILURE);

    switch(file_type)
    {
        case MONITER_FILE:
        case MASTER_CTRL_FILE:
        case AUXI_CTRL_FILE:
        case CPLD_FILE:
        {
            g_opt = FLASH_OPT;
            ret = mqtt_download_file(url, update_file_name[file_type], NOR_APP_DOWNLOAD, file_size, file_crc, which_tls);
            break;
        }
        case DIFF_FILE:
        {
            g_opt = FLASH_OPT;
            char diff_file_name[DIFF_UPDATE_NAME_LEN] = {0};
            rt_snprintf(diff_file_name, DIFF_UPDATE_NAME_LEN - 1, "pv_diff_V%s.bin", g_file_info[0].file_version);
            ret = mqtt_download_file(url, diff_file_name, NOR_APP_DOWNLOAD, file_size, file_crc, which_tls);
            break;
        }
        case MQTT_CERT:
        {
            g_opt = FILE_SYS_OPT;
            if(which_tls == DEF_TLS)
            {
                ret = download_files(cert_files, url, file_size, file_crc);
            }
            else if(which_tls == OTH_TLS)
            {
                ret = download_files(cert_other_files, url, file_size, file_crc);
            }
            else
            {
                return FAILURE;
            }

            send_msg_to_thread(CERT_ONE_CHG_MSG, MOD_SYS_MANAGE, NULL, 0);
            if(ret >= 0)
            {   
                send_msg_to_thread(CERT_CHG_MSG, MOD_MQTT, &which_tls_msg, sizeof(which_tls_msg));
                return SUCCESSFUL;
            }
            else
            {
                return FAILURE;
            }

            break;
        }
        default:
            break;
    }
    return ret;
}



int network_update_deal(int opt, int file_type, unsigned int file_size, unsigned int file_crc, short addr_info)
{
    int ret = 0;
    unsigned char* download_sdram = get_file_sdram(opt);

     if(download_sdram == NULL)
    {
        LOG_E("network_download_file| opt:%d error", opt);
        return FAILURE;
    }
    part_data_t data = {0};
    if(file_type == DIFF_FILE)
    {
        rt_snprintf_s(data.name, sizeof(data.name), UPDATE_PATCH_PART);
    }
    else
    {
        rt_snprintf_s(data.name, sizeof(data.name), UPDATE_DOWNLOAD_PART);//升级文件存储flash位置
    }

    ret = storage_process(&data, erase_opr);     // flash先擦后写
    if(ret != SUCCESSFUL)
    {
        LOG_E("network_download_file| erase flash fail");
        return FAILURE;
    }
    data.buff = download_sdram;
    data.len = file_size;
    data.offset = 0;

    ret = storage_process(&data, write_opr);
    if(ret != SUCCESSFUL)
    {
        LOG_E("network_download_file| write flash fail");
        return FAILURE;
    }

    mqtt_start_update(addr_info, file_size,file_crc);
    return SUCCESSFUL;
}

int send_update_result(void)
{
    char topic[MQTT_TOPIC_LEN_MAX] = {0};
    char site_id[SITE_ID_LEN] = {0};
    char device_id[STR_LEN_16] = {0};
    char sn[STR_LEN_32] = {0};
    unsigned char update_rst = 0;
    unsigned char delay_update = 2;
    unsigned char update_status = DEVICE_NO_UPDATE;
    cJSON* json_data = NULL;
    cJSON* json_array = NULL;
    update_app_info_t update_info = {0};
    pv_parallel_status_t parallel_status = {};

    handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&update_info, sizeof(update_info), MQTT_UPDATE_INFO_OFFSET);
    if (isalnum((int)(update_info.sub_uuid[0])) == RT_FALSE || update_info.real_addr_info == 0)
    {
        return FAILURE;
    }

    json_data = cJSON_CreateObject();
    if (json_data == NULL)
    {
        LOG_E("send_update_info create json error!\n");
        return FAILURE;
    }

    get_device_id(get_master_addr() - 1, device_id);
    handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&parallel_status.dev_sn, sizeof(parallel_status.dev_sn), DEV_SN_OFFSET);
    rt_memcpy_s(parallel_status.dev_sn[get_master_addr() - 1], SN_LEN, device_id, SN_LEN);

    cJSON_AddStringToObject(json_data, "msgId", update_info.sub_uuid);
    cJSON_AddNumberToObject(json_data, "time", mqtt_get_timestamp());

    json_array = cJSON_CreateArray();
    unsigned short addr_info = update_info.real_addr_info;
    for(int loop = 0; loop < PV_NUM; loop ++)
    {
        if((addr_info & (1 << (loop + 1))) == 0)
        {
            continue;
        }
        update_rst = deal_mqtt_update_rst(loop);
        rt_memcpy_s(sn, SN_LEN, parallel_status.dev_sn[loop], SN_LEN);
        cJSON* update_result = NULL;
        update_result = cJSON_CreateObject();
        cJSON_AddStringToObject(update_result, "sn",  sn);
        cJSON_AddNumberToObject(update_result, "rtn", update_rst);
        cJSON_AddItemToArray(json_array, update_result);
        rt_kprintf("update_result | sn:%s rtn:%d\n", sn, update_rst);
    }

    cJSON_AddItemToObject(json_data, "result", json_array);

    for (int idx = 0; idx < MAX_CLIENT; idx++)
    {   
        get_site_id(idx, site_id);
        rt_snprintf(topic, MQTT_TOPIC_LEN_MAX,
                "/zte/up%s/inv/%s/update", site_id, device_id);
        send_mqtt_data(get_mqtt_client(idx), topic, json_data);
    }
    deal_update_dev_info(delay_update, update_info.real_addr_info);
    deal_version_info(&update_info);
    rt_memset(update_info.sub_uuid, 0x00, sizeof(update_info.sub_uuid));
    rt_memset(update_info.is_enter_update, 0x00, sizeof(update_info.is_enter_update));
    rt_memset(update_info.version, 0x00, sizeof(update_info.version));
    rt_memset(g_real_update_status, 0, PV_NUM);
    update_info.real_addr_info = 0;
    update_info.cur_addr_info = 0;
    update_info.update_addr_info = 0;
    handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&update_info, sizeof(update_info), MQTT_UPDATE_INFO_OFFSET);
    set_one_data(DAC_DATA_ID_PV_UPDATE_STATUS,&update_status);
    g_update_start_flag = FALSE;
    cJSON_Delete(json_data);
    return update_rst;
}

unsigned char deal_mqtt_update_rst(int loop)
{
    unsigned char update_rst = 0;
    if (g_real_update_status[loop] == MONITER_UPDATE_SUCCESS || (g_real_update_status[loop] == SOUTH_UPDATE_SUCCESS && g_update_file_type != 0 ))
    {
        update_rst = 0;    // 升级成功
    }
    else
    {
        update_rst = 1;    
    }
    return update_rst;
}

int deal_version_info(update_app_info_t* update_info)
{
    int master_addr = 0;
    int addr = 1;
    unsigned int update_dev = 0;
    unsigned char version_sid_idx = 1;
    unsigned short master_sid_version[]={DAC_PARA_ID_CSC_VERSION_OFFSET,DAC_PARA_ID_PSC_VERSION_OFFSET};
    unsigned int slave_sid_version[]={DAC_PO_PARA_ID_CSC_VERSION,DAC_PO_PARA_ID_PSC_VERSION};
    if(g_update_file_type == 0)
    {
        version_sid_idx = 0;
    }
    RETURN_VAL_IF_FAIL(update_info != NULL, FAILURE);
    get_one_para(DAC_COMMON_ID_RS485_COMMU_ADDR_OFFSET, &master_addr);
    while(addr <= PV_NUM)
    {
        update_dev = ((update_info->update_addr_info >> addr) & 1);
        if(update_dev && addr == master_addr && (g_real_update_status[addr - 1] == MONITER_UPDATE_SUCCESS || g_real_update_status[addr - 1] == SOUTH_UPDATE_SUCCESS))
        {
            set_one_para(master_sid_version[version_sid_idx], update_info->version, TRUE, FALSE);
            save_version_event(master_sid_version[version_sid_idx], &(update_info->version[TAR_VER_INFO_INDEX]),PARA_TYPE);
        }
        else if(update_dev && (g_real_update_status[addr - 1] == MONITER_UPDATE_SUCCESS || g_real_update_status[addr - 1] == SOUTH_UPDATE_SUCCESS))
        {
            set_slave_version(slave_sid_version[version_sid_idx], update_info->version, addr);
        }
        addr++;
    }
    return SUCCESSFUL;
}

int set_slave_version(unsigned int po_sid, char* version, int slave_addr)
{
    char send_data[5] = {0};
    send_data[0] = slave_addr;
    rt_memcpy(&send_data[1], &po_sid, sizeof(po_sid));
    set_one_data(po_sid, version);
    send_msg_to_thread(SET_SLAVE_ONE_PARA, MOD_INVERTER, send_data, sizeof(send_data));
    return SUCCESSFUL;
}

void handle_send_update_msg(_rt_msg_t curr_msg)
{
    send_update_result();
}

int process_download_result(int download_rst, unsigned short addr_info, unsigned int file_size)
{   
    int dev_index = 0; 
    if(download_rst < 0)
    {
        send_msg_to_thread(MQTT_UPDATE_RESULT_MSG, MOD_MQTT, NULL, 0);
        return MQTT_REQ_ERR;
    }

    if((g_update_file_type <= CPLD_FILE || g_update_file_type == DIFF_FILE) && addr_info > 0)
    {   
        if(g_update_file_type >= MASTER_CTRL_FILE && g_update_file_type != DIFF_FILE)
        {
            dev_index = g_update_file_type - 1;
        }

        send_update_msg_to_sys_manage(g_opt, g_update_file_type, file_size, g_file_info[dev_index].file_crc, addr_info);
    }
    return MQTT_SUCCESS;
}


int deal_delay_update_info(unsigned char delay_update, unsigned short addr_info)
{
    deal_update_dev_info(delay_update, addr_info);//同步延迟升级参数
    // 如果不需要延迟更新，则启动升级
    if(delay_update == RT_FALSE)
    {
        send_msg_to_thread(MQTT_UPDATE, MOD_MQTT, NULL, 0);
        return SUCCESSFUL;
    }

    // 开启检测升级设备是否满足延迟升级条件定时器
    rt_timer_start(g_delay_update_info_timer);
    
    return SUCCESSFUL;
}


int deal_update_dev_info(unsigned char delay_update, unsigned short addr_info)
{
    int addr = 1;
    char send_data[5] = {0};
    unsigned int po_sid = DAC_PO_PARA_ID_DELAY_UPDATE;
    unsigned char master_addr = get_master_addr();

    set_one_para(DAC_PARA_ID_DELAY_UPDATE_OFFSET,  &delay_update, RT_TRUE, RT_TRUE);
    for(addr = 1; addr <= PV_NUM; addr++)
    {
        if((addr != master_addr) && ((addr_info >> addr) & 1))
        {
            send_data[0] = addr;
            rt_memcpy(&send_data[1], &po_sid, sizeof(po_sid));
            set_one_data(po_sid, &delay_update);
            send_msg_to_thread(SET_SLAVE_ONE_PARA, MOD_INVERTER, send_data, sizeof(send_data));
            rt_thread_mdelay(1000);
        }
    }
    return SUCCESSFUL;
}

int deal_ack_updating(char* uuid, unsigned short addr_info)
{
    char topic[MQTT_TOPIC_LEN_MAX] = {0};
    char site_id[SITE_ID_LEN] = {0};
    char device_id[STR_LEN_16] = {0};
    char sn[STR_LEN_32] = {0};
    unsigned char update_rst = 0;
    cJSON* json_data = NULL;
    cJSON* json_array = NULL;

    json_data = cJSON_CreateObject();
    if (json_data == NULL)
    {
        LOG_E("send_update_info create json error!\n");
        return FAILURE;
    }

    get_device_id(get_master_addr() - 1, device_id);

    cJSON_AddStringToObject(json_data, "msgId", uuid);
    cJSON_AddNumberToObject(json_data, "time", mqtt_get_timestamp());

    json_array = cJSON_CreateArray();
    for(int loop = 0; loop < PV_NUM; loop ++)
    {
        if((addr_info & (1 << (loop + 1))) == 0)
        {
            continue;
        }
        update_rst = 12;
        get_device_id(loop, sn);
        cJSON* update_result = NULL;
        update_result = cJSON_CreateObject();
        cJSON_AddStringToObject(update_result, "sn",  sn);
        cJSON_AddNumberToObject(update_result, "rtn", update_rst);
        cJSON_AddItemToArray(json_array, update_result);
        rt_kprintf("update_result | sn:%s rtn:%d\n", sn, update_rst);
    }

    cJSON_AddItemToObject(json_data, "result", json_array);

    for (int idx = 0; idx < MAX_CLIENT; idx++)
    {   
        get_site_id(idx, site_id);
        rt_snprintf(topic, MQTT_TOPIC_LEN_MAX,
                "/zte/up%s/inv/%s/update", site_id, device_id);
        send_mqtt_data(get_mqtt_client(idx), topic, json_data);
    }

    return SUCCESSFUL;

}

int handle_update(void* client, message_data_t* msg)
{
    cJSON* recv_json = NULL;
    cJSON* sns       = NULL;
    cJSON *sns_item  = NULL;
    update_app_info_t update_info = {0};
    char uuid[UUID_LEN] = {0};
    unsigned char update_status = 0;
    unsigned char addr = 0;
    unsigned short addr_info = 0;
    char* url = NULL;
    int ret = 0, cur_dev = 0, id_num = 0, idx = 0;
    unsigned char delay_update_val = 0;

    if (client == NULL || msg == NULL || msg->message == NULL)
    {
        return MQTT_NULL_ERR;
    }
    ret = parse_msg_sn(msg->topic_name, &cur_dev);
    if (ret != MQTT_SUCCESS)
    {
        return MQTT_SCOPE_ERR;
    }

    recv_json = mqtt_parse_msg(msg->message->payload, uuid);
    if (recv_json == NULL)
    {
        return MQTT_NULL_ERR;
    }
    //协议解析
    sns = cJSON_GetObjectItem(recv_json, "sns");
    cJSON_ArrayForEach(sns_item, sns)
    {
        addr = get_addr_by_device_id(sns_item->valuestring);
        rt_kprintf("handle_update|sn:%s\n", sns_item->valuestring);
        if(0xFF == addr)
        {
            continue;
        }
        addr_info += 1 << addr;
    }
    handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&update_info, sizeof(update_info), MQTT_UPDATE_INFO_OFFSET);
    get_one_data(DAC_DATA_ID_PV_UPDATE_STATUS, &update_status);
    if(isalnum((int)(update_info.sub_uuid[0])) != RT_FALSE || update_status != DEVICE_NO_UPDATE)
    {
        deal_ack_updating(uuid, addr_info);
        return MQTT_REQ_ERR;
    }
    g_which_tls = which_client(client);
    rt_memset(g_file_info, 0, sizeof(g_file_info));
    cJSON* file_path = cJSON_GetObjectItem(recv_json, "url");
    url = cJSON_GetStringValue(file_path);
    rt_snprintf(g_update_file_url, FILE_PATH_LEN - 1, "%s", url);//保存路径
    cJSON* delay_update = cJSON_GetObjectItem(recv_json, "delay_update");
    delay_update_val = cJSON_GetNumberValue(delay_update);
    cJSON* file_version = cJSON_GetObjectItem(recv_json, "version");
    char* version = cJSON_GetStringValue(file_version);
    cJSON* recv_arr = cJSON_GetObjectItem(recv_json, "update_info");
    id_num = cJSON_GetArraySize(recv_arr);
    for(idx = 0; idx < id_num; idx++)
    {
        cJSON* data = cJSON_GetArrayItem(recv_arr, idx);
        cJSON* update_file_type = cJSON_GetObjectItem(data, "file_type");
        g_file_info[idx].file_type = cJSON_GetNumberValue(update_file_type);
        cJSON* update_version = cJSON_GetObjectItem(data, "version");
        char* update_file_version = cJSON_GetStringValue(update_version);
        rt_memcpy(g_file_info[idx].file_version, update_file_version, rt_strnlen(update_file_version, STR_LEN_16));
        cJSON* update_file_crc = cJSON_GetObjectItem(data, "file_crc");
        g_file_info[idx].file_crc = cJSON_GetNumberValue(update_file_crc);
    }
    cJSON_Delete(recv_json);
    //保存升级信息内容
    g_real_addr_info = addr_info;
    update_info.real_addr_info = addr_info;
    update_info.update_addr_info = addr_info;
    update_info.cur_addr_info = addr_info;
    rt_memcpy(update_info.sub_uuid, uuid, sizeof(update_info.sub_uuid));
    rt_memcpy(update_info.version, version, rt_strnlen(version, STR_LEN_32));
    rt_memset(update_info.is_enter_update, 0, sizeof(update_info.is_enter_update));
    rt_memset(g_real_update_status, 0, PV_NUM);
    handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&update_info, sizeof(update_info), MQTT_UPDATE_INFO_OFFSET);
    //记录此时升级的启动时间
    g_update_start_time = time(RT_NULL);
    LOG_E("%s:%d| update_start_time:%lld", __FUNCTION__ , __LINE__, g_update_start_time);
    pv_parallel_status_t* parallel_status = get_parallel_status();
    handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)parallel_status->dev_sn, sizeof(parallel_status->dev_sn), DEV_SN_OFFSET);
    //处理延迟升级使能信息
    deal_delay_update_info(delay_update_val, addr_info);
    g_update_start_flag = TRUE;
    update_status = DEVICE_MQTT_UPDATING;
    set_one_data(DAC_DATA_ID_PV_UPDATE_STATUS, &update_status);
    return MQTT_SUCCESS;
}

int mqtt_start_update(unsigned short addr_info, unsigned int file_size, unsigned int file_crc)
{
    char parallel_update_msg[11] = {0};
    unsigned char addr = 0;
    update_app_info_t update_info = {0};

    handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&update_info, sizeof(update_info), MQTT_UPDATE_INFO_OFFSET);
    update_info.cur_addr_info = addr_info;
    handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&update_info, sizeof(update_info), MQTT_UPDATE_INFO_OFFSET);
    get_one_para(DAC_COMMON_ID_RS485_COMMU_ADDR_OFFSET, &addr);
    LOG_E("handle_update|master_addr:%d, addr_info:0x%x\n", addr, addr_info);
    if(addr_info == (1 << addr))           //只有主机的地址
    {
       deal_update(g_update_file_type, file_size, file_crc);
    }
    else
    {
        rt_memset(parallel_update_msg, 0, 7);
        *(short*)parallel_update_msg = addr_info;
        *(unsigned int*)(parallel_update_msg + 2) = file_size;
        *(char*)(parallel_update_msg + 6) = g_update_file_type;
        *(unsigned int*)(parallel_update_msg + 7) = file_crc;
        send_msg_to_thread(PARALLEL_UPDATE_MSG, MOD_INVERTER, parallel_update_msg, sizeof(parallel_update_msg));
        rt_kprintf("inform north to update\n");
    }
    return SUCCESSFUL;
}

int deal_auxi_update(update_app_info_t* update_info)
{
    unsigned short addr_info = 0;
    addr_info = update_info->update_addr_info;
    judge_power_file_version(&addr_info, AUXI_INDX);
    update_info->cur_addr_info = addr_info;
    handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)update_info, sizeof(update_app_info_t), MQTT_UPDATE_INFO_OFFSET);
    if(addr_info == 0)
    {
        g_update_file_type--;
    }
    else
    {
        deal_file_download(update_info, addr_info);
        return SUCCESSFUL;
    }
    return FAILURE;
}

void handle_power_parallel_update(update_app_info_t* update_info)
{
    unsigned short addr_info = 0;
    int ret = 0;

    g_update_file_type--;//启动下一个升级
    LOG_E("handle_power_parallel_update | g_update_file_type:%d\n",g_update_file_type);
    if(g_update_file_type == AUXI_CTRL_FILE)
    {
        ret = deal_auxi_update(update_info);
        RETURN_IF_FAIL(ret == FAILURE);
    }
    addr_info = update_info->update_addr_info;
    judge_power_file_version(&addr_info, MASTER_INDX);
    if(addr_info != 0)
    {
        deal_file_download(update_info, addr_info);
        return;
    }
    send_msg_to_thread(MQTT_UPDATE_RESULT_MSG, MOD_MQTT, NULL, 0);
    return;
}

int deal_file_download(update_app_info_t* update_info, unsigned short addr_info)
{
    unsigned int file_size = 0, file_crc = 0;
    int ret = 0;
    ret = download_file(g_update_file_url, g_update_file_type, &file_size, &file_crc, g_which_tls);
    update_info->cur_addr_info = addr_info;
    handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)update_info, sizeof(update_app_info_t), MQTT_UPDATE_INFO_OFFSET);
    if(ret < 0)
    {
        rt_memset(g_real_update_status, 0 ,PV_NUM);
        send_msg_to_thread(MQTT_UPDATE_RESULT_MSG, MOD_MQTT, NULL, 0);
        return FAILURE;
    }
    send_update_msg_to_sys_manage(g_opt, g_update_file_type, file_size, file_crc, addr_info);
    return SUCCESSFUL;
}

void handle_deal_update_res(_rt_msg_t curr_msg)
{
    judge_update_rst();
}

int create_mqtt_send_update_timer()
{
    const char* timer_name = "update_timer";
    rt_timer_t mqtt_update_timer = NULL;
    mqtt_update_timer = rt_timer_create(timer_name, send_mqtt_update_msg, NULL, MONITER_UPDATE_WAITE, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    if (mqtt_update_timer == NULL)
    {
        LOG_E("mqtt_update_timer fail\n");
        return FAILURE;
    }
    set_mqtt_update_timer(mqtt_update_timer);
    g_delay_update_info_timer = rt_timer_create("delay_update_info_timer", delay_update_status_timeout, NULL, CHECK_DELAY_UPDATE_STATUS_TIME, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    if (g_delay_update_info_timer == NULL)
    {
        LOG_E("g_delay_update_info_timer fail\n");
        return FAILURE;
    }
    return SUCCESSFUL;
}
void send_mqtt_update_msg(void *parameter)
{
    int dev_addr = 0;
    rt_timer_stop(get_mqtt_update_timer());
    for(dev_addr = 1; dev_addr <= PV_NUM; dev_addr++)
    {
        send_get_msg_to_slave(dev_addr, GET_SLAVE_REAL_DATA);
    }
    rt_thread_mdelay(10000);
    send_msg_to_thread(MQTT_DEAL_UPDATE_RESULT, MOD_MQTT, NULL, 0);
}
void delay_update_status_timeout(void *parameter)
{
    send_msg_to_thread(JUDGE_DELAY_UPDATE_STATUS, MOD_MQTT, NULL, 0);
}

void handle_delay_update_status(_rt_msg_t curr_msg)
{
    judge_delay_update_status();
}

int judge_delay_update_status()
{
    int addr = 1;
    unsigned int delay_update_status = 0;
    unsigned char master_addr = get_master_addr();
    time_t cur_time = time(RT_NULL);
    if(cur_time - g_update_start_time >= ONE_DAY_SECOND)//需要停止延迟升级检测
    {
        LOG_E("%s:%d|cur time:%lld,start time:%lld\n", __FUNCTION__ , __LINE__, cur_time, g_update_start_time);
        send_update_status("DelayUpdateTimeout", UPDATE_NO_START);
        rt_timer_stop(g_delay_update_info_timer);
        send_msg_to_thread(MQTT_DEAL_UPDATE_RESULT, MOD_MQTT, NULL, 0);
        return FAILURE;
    }
    // 检查主机的延迟更新状态
    if((g_real_addr_info >> master_addr) & 1)
    {
        get_one_data(DAC_DATA_ID_DELAY_UPDATE_STATUS, &delay_update_status);
        if(delay_update_status == 0)
        {
            return FAILURE;
        }
    }
    
    // 遍历所有从机，检查它们的延迟更新状态
    for(addr = 1; addr <= PV_NUM; addr++)
    {
        delay_update_status = 0;
        if(addr != master_addr && (g_real_addr_info >> addr) & 1)
        {
            get_one_comm(0, addr, DAC_PO_DATA_ID_DELAY_UPDATE_STATUS, CMD_REALDATA, &delay_update_status);
            if(delay_update_status == 0)
            {
                return FAILURE;
            }
        }
    }
    
    // 如果所有从机的延迟更新状态都为非零，则停止定时器并发送升级消息
    rt_timer_stop(g_delay_update_info_timer);
    send_msg_to_thread(MQTT_UPDATE, MOD_MQTT, NULL, 0);
    return SUCCESSFUL;
}



int deal_update_rst(update_app_info_t* update_info, void* parallel_update_info, unsigned char addr)
{
    unsigned short real_addr_info = 0, cur_addr_info = 0;
    parallel_update_info_t* parallel_update_data = (parallel_update_info_t*)parallel_update_info;
    real_addr_info = (update_info->update_addr_info >> addr) & 1;
    cur_addr_info = (update_info->cur_addr_info >> addr) & 1;
    LOG_E("addr:%d real_addr_info:%d cur_addr_info:%d\n",addr, real_addr_info, cur_addr_info);
    if(cur_addr_info == real_addr_info && real_addr_info == 1)//执行了升级
    {
        if(update_info->is_enter_update[addr - 1] == TRUE)
        {
            g_real_update_status[addr - 1] = parallel_update_data->update_status[addr - 1];    //从机确实触发过
        }
        else
        {
            g_real_update_status[addr - 1] = 0;  //从机未被触发，直接判定为升级失败

            update_info->update_addr_info ^= (1 << addr);
            LOG_E("g_real_update_status:%d | addr:%d\n",g_real_update_status[addr - 1], addr);
            return SUCCESSFUL;
        }
        
        if(parallel_update_data->update_status[addr - 1] != MONITER_UPDATE_SUCCESS 
                                && parallel_update_data->update_status[addr - 1] != SOUTH_UPDATE_SUCCESS)
        {
            update_info->update_addr_info ^= (1 << addr);
        }

    }
    else if(cur_addr_info != real_addr_info)//版本号一致，不需要升级，结果为升级成功
    {
        g_real_update_status[addr - 1] = 1;
    }
    LOG_E("g_real_update_status:%d | addr:%d\n",g_real_update_status[addr - 1], addr);
    return SUCCESSFUL;
}

//判断升级结果（监控：直接上送升级结果；功率：失败的设备不继续升级，成功的继续升级后再上送升级结果）
int judge_update_rst()
{
    unsigned char addr = 1;
    update_app_info_t update_info = {0};
    parallel_update_info_t* parallel_update_info = get_parallel_update_info();

    handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&update_info, sizeof(update_info), MQTT_UPDATE_INFO_OFFSET);
    if (isalnum((int)(update_info.sub_uuid[0])) == RT_FALSE)
    {
        return FAILURE;
    }
    get_one_para(DAC_COMMON_ID_RS485_COMMU_ADDR_OFFSET, &parallel_update_info->master_addr);
    get_one_data(DAC_DATA_ID_UPDATE_STATUS, &parallel_update_info->update_status[parallel_update_info->master_addr - 1]);
    while(addr <= PV_NUM)
    {
        deal_update_rst(&update_info, parallel_update_info,addr);
        addr++;
    }
    handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&update_info, sizeof(update_info), MQTT_UPDATE_INFO_OFFSET);
    if(update_info.update_addr_info != 0 && g_update_file_type <= CPLD_FILE && g_update_file_type > MASTER_CTRL_FILE)
    {
        handle_power_parallel_update(&update_info);
    }
    else
    {
        send_msg_to_thread(MQTT_UPDATE_RESULT_MSG, MOD_MQTT, NULL, 0);//上送结果
    }
    return SUCCESSFUL;
}



int get_moniter_update_rst()
{
    update_app_info_t data = {0};
    handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&data, sizeof(data), MQTT_UPDATE_INFO_OFFSET);
    if(isalnum((int)(data.sub_uuid[0])) == RT_FALSE || g_update_start_flag != 0)
    {
        return FAILURE;
    }
    start_update_timer(MONITER_UPDATE_WAITE);
    return SUCCESSFUL;
}

int handle_mqtt_update()
{
    update_app_info_t update_info = {0};
    unsigned short addr_info = 0;
    unsigned int file_size = 0, file_crc = 0;
    int download_rst = 0;
    handle_storage(read_opr, EE_PUBLIC_INFO, (unsigned char*)&update_info, sizeof(update_info), MQTT_UPDATE_INFO_OFFSET);
    if(isalnum((int)(update_info.sub_uuid[0])) == RT_FALSE)
    {
        return FAILURE;
    }
    addr_info = update_info.real_addr_info;
    g_update_file_type = judge_download_file(&addr_info, update_info.real_addr_info);
    update_info.cur_addr_info = addr_info;
    handle_storage(write_opr, EE_PUBLIC_INFO, (unsigned char*)&update_info, sizeof(update_info), MQTT_UPDATE_INFO_OFFSET);
    rt_kprintf("g_update_file_type:%d addr_info%d\n",g_update_file_type, addr_info);
    if(addr_info == 0)//设备版本与文件一致，不需要升级，直接处理升级结果。
    {
        judge_update_rst();
    }
    else
    {
        download_rst = download_file(g_update_file_url, g_update_file_type, &file_size, &file_crc, g_which_tls);
    }

    return process_download_result(download_rst, addr_info, file_size);
}

void handle_mqtt_update_msg(_rt_msg_t curr_msg)
{
    handle_mqtt_update();
}