#ifndef _MQTTT_MAIN_H_
#define _MQTTT_MAIN_H_

#ifdef __cplusplus
extern "C" {
#endif    //  __cplusplus
#include "sdram_memheap.h"

#ifndef BSP_USING_SDRAM
#define MQTT_MALLOC  rt_malloc
#define MQTT_FREE    rt_free
#else
#define MQTT_MALLOC  rt_sdram_malloc
#define MQTT_FREE    rt_sdram_free
#endif

void* init_mqtt(void* param);
void mqtt_thread_entry(void* thread_data);
int mqtt_process_recv_msg(void);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif // _MQTTT_MAIN_H_
