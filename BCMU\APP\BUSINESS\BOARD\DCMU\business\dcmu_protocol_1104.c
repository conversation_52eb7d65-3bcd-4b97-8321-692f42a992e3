#include <string.h>
#include <stdlib.h>
#include <rtthread.h>
#include "sps.h"
#include "temp.h"
#include "dev_dcmu.h"
#include "dcmu_protocol_1104.h"
#include "alarm_mgr_api.h"
#include "protocol_layer.h"
#include "realdata_id_in.h"
#include "alarm_id_in.h"
#include "para_id_in.h"
#include "realdata_save.h"
#include "protocol_1363_comm.h"
#include "utils_data_transmission.h"
#include "utils_rtthread_security_func.h"
#include "utils_data_type_conversion.h"
#include "para_manage.h"
#include "alarm_register.h"
#include "alarm_manage.h"
#include "utils_time.h"
#include "pdt_version.h"

Static int pack_dc_alm_data(void* dev_inst, void* cmd_buff);
Static int pack_env_alm_data(void* dev_inst, void* cmd_buff);
Static int pack_env_analog_float_data(void* dev_inst, void* cmd_buff);
Static int pack_dcem_fact_data(void* dev_inst, void *cmd_buf);
Static int pack_sys_time_data(void* dev_inst, void *cmd_buf);
Static int parse_sys_time_data(void* dev_inst, void *cmd_buf);
Static int pack_dc_analog_float_data(void* dev_inst, void* cmd_buff);
Static int pack_dc_switch_data(void* dev_inst, void* cmd_buff);
Static int pack_dc_para_float(void* dev_inst, void* cmd_buff);
Static int pack_env_para_float(void* dev_inst, void* cmd_buff);
Static int parse_dc_para_float(void* dev_inst, void* cmd_buff);
Static int parse_env_para_float(void* dev_inst, void* cmd_buff);


static s1363_cmd_head_t cmd_req[] RAM_SECTION = 
{
    {VER_20 , CID1_DC , DCMU_CMD_GET_ALM},                       //0
    {VER_20 , CID1_DC , DCMU_CMD_GET_SYS_TIME},                  //1
    {VER_20 , CID1_DC , DCMU_CMD_SET_SYS_TIME},                  //2
    {VER_20 , CID1_DC , DCMU_CMD_GET_COMMPROTOCOL_VER},          //3
    {VER_20 , CID1_DC , DCMU_CMD_GET_DEVICE_ADDR},               //4
    {VER_20 , CID1_DC , DCMU_CMD_GET_DCEMFACTORY_INFO},              //5
    {VER_20 , CID1_ENV , DCMU_CMD_GET_ALM},                          //6
    {VER_20 , CID1_ENV , DCMU_CMD_GET_ANALOG},                 //7
    {VER_20 , CID1_DC , DCMU_CMD_GET_ANALOG},                  //8
    {VER_20 , CID1_DC , DCMU_CMD_GET_SWITCH_STATUS},                 //9
    {VER_20 , CID1_DC , DCMU_CMD_GET_PARA},                 //10
    {VER_20 , CID1_DC , DCMU_CMD_SET_PARA},                 //11
    {VER_20 , CID1_ENV , DCMU_CMD_GET_PARA},               //12
    {VER_20 , CID1_ENV , DCMU_CMD_SET_PARA},               //13
};

static s1363_cmd_head_t cmd_ack[] RAM_SECTION = 
{
    {VER_20 , CID1_DC , DCMU_CMD_GET_ALM},                       //0
    {VER_20 , CID1_DC , DCMU_CMD_GET_SYS_TIME},                  //1
    {VER_20 , CID1_DC , DCMU_CMD_SET_SYS_TIME},                  //2
    {VER_20 , CID1_DC , DCMU_CMD_GET_COMMPROTOCOL_VER},          //3
    {VER_20 , CID1_DC , DCMU_CMD_GET_DEVICE_ADDR},               //4
    {VER_20 , CID1_DC , DCMU_CMD_GET_DCEMFACTORY_INFO},              //5
    {VER_20 , CID1_ENV , DCMU_CMD_GET_ALM},                          //6
    {VER_20 , CID1_ENV , DCMU_CMD_GET_ANALOG},                 //7
    {VER_20 , CID1_DC , DCMU_CMD_GET_ANALOG},                  //8
    {VER_20 , CID1_DC , DCMU_CMD_GET_SWITCH_STATUS},                 //9
    {VER_20 , CID1_DC , DCMU_CMD_GET_PARA},                 //10
    {VER_20 , CID1_DC , DCMU_CMD_SET_PARA},                 //11
    {VER_20 , CID1_ENV , DCMU_CMD_GET_PARA},               //12
    {VER_20 , CID1_ENV , DCMU_CMD_SET_PARA},               //13
};

static cmd_t no_poll_cmd_tab[] = 
{
    {DCMU_GET_DC_ALM_DATA, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(s1363_cmd_head_t),   NULL, NULL, pack_dc_alm_data,NULL, },
    {DCMU_GET_SYS_TIME, CMD_PASSIVE, &cmd_req[1], &cmd_ack[1], sizeof(s1363_cmd_head_t),   NULL, NULL, pack_sys_time_data, NULL,},
    {DCMU_SET_SYS_TIME, CMD_PASSIVE, &cmd_req[2], &cmd_ack[2], sizeof(s1363_cmd_head_t),   NULL, NULL,  NULL,parse_sys_time_data, },
    {DCMU_GET_COMMPROTOCOL_VER, CMD_PASSIVE, &cmd_req[3], &cmd_ack[3], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, NULL,},
    {DCMU_GET_DEVICE_ADDR, CMD_PASSIVE, &cmd_req[4], &cmd_ack[4], sizeof(s1363_cmd_head_t),   NULL, NULL,  NULL,NULL, }, 
    {DCMU_GET_DCEMFACTORY_INFO,  CMD_PASSIVE, &cmd_req[5], &cmd_ack[5], sizeof(s1363_cmd_head_t),   NULL, NULL, pack_dcem_fact_data, NULL, },
    {DCMU_GET_ENV_ALM_DATA, CMD_PASSIVE, &cmd_req[6], &cmd_ack[6], sizeof(s1363_cmd_head_t),   NULL, NULL, pack_env_alm_data,NULL, },
    {DCMU_GET_ENV_ANALOG_FLOAT, CMD_PASSIVE, &cmd_req[7], &cmd_ack[7], sizeof(s1363_cmd_head_t),   NULL, NULL, pack_env_analog_float_data,NULL, },
    {DCMU_GET_DC_ANALOG_FLOAT, CMD_PASSIVE, &cmd_req[8], &cmd_ack[8], sizeof(s1363_cmd_head_t),   NULL, NULL, pack_dc_analog_float_data,NULL, },
    {DCMU_GET_DC_SWITCH_STATUS, CMD_PASSIVE, &cmd_req[9], &cmd_ack[9], sizeof(s1363_cmd_head_t),   NULL, NULL, pack_dc_switch_data,NULL, },
    {DCMU_GET_DC_PARA_FLOAT, CMD_PASSIVE, &cmd_req[10], &cmd_ack[10], sizeof(s1363_cmd_head_t),   NULL, NULL, pack_dc_para_float, NULL, },
    {DCMU_SET_DC_PARA_FLOAT, CMD_PASSIVE, &cmd_req[11], &cmd_ack[11], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, parse_dc_para_float, },
    {DCMU_GET_ENV_PATA_FLOAT, CMD_PASSIVE, &cmd_req[12], &cmd_ack[12], sizeof(s1363_cmd_head_t),   NULL, NULL, pack_env_para_float, NULL, },
    {DCMU_SET_ENV_PARA_FLOAT, CMD_PASSIVE, &cmd_req[13], &cmd_ack[13], sizeof(s1363_cmd_head_t),   NULL, NULL, NULL, parse_env_para_float, },
    {0},
};

Static dev_type_t dev_dcmu_1104 = 
{
    DEV_NORTH_1104, 1, PROTOCOL_YD_1363, LINK_DCMU, R_BUFF_LEN, S_BUFF_LEN, 0, &no_poll_cmd_tab[0],
};

dev_type_t* init_dev_dcmu_1104(void){
    return &dev_dcmu_1104;
}

Static int pack_dc_alm_data(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    int alm_id = 0;
    unsigned char* buff;
    unsigned char offset = 0,data_flag;
    buff = cmd_buf_temp->buf;

    /* DATA_FLAG  */
    get_one_data(DCMU_DATA_ID_DATA_FLAG,&data_flag);
    buff[offset++] = data_flag & 0xFE;
    
    /* WARN_STATE */
    buff[offset++] = 0x01;                    //直流屏数量为1

    alm_id = GET_ALM_ID(DCMU_ALM_ID_DC_VOLTAGE_ALARM,1,1);// 直流电压状态
    buff[offset++] = get_realtime_alarm_value(alm_id);

    buff[offset++] = SWITCH_NUM;                    // 直流负载熔丝开关数量
    for (char i = 0; i < SWITCH_NUM; i++)
    {
        alm_id = GET_ALM_ID(DCMU_ALM_ID_DC_LOAD_CIRCUIT_BROKEN + i,1,1);// 直流负载熔丝开关状态
        buff[offset++] = get_realtime_alarm_value(alm_id);
    }
    
    buff[offset++] = 0x01;        // 自定义告警数量为1

    alm_id = GET_ALM_ID(DCMU_ALM_ID_DC_LIGHTNING_PROTECTOR_DAMAGED,1,1);//直流防雷器告警
    buff[offset++] = get_realtime_alarm_value(alm_id);

    /* LENID */
    cmd_buf_temp->data_len = offset;
    
    return SUCCESSFUL;
}

Static int pack_env_alm_data(void* dev_inst, void* cmd_buff)
{
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    int alm_id = 0;
    unsigned char* buff;
    unsigned char offset = 0,data_flag;
    char alarm_status = 0;
    buff = cmd_buf_temp->buf;

    /* DATA_FLAG */
    get_one_data(DCMU_DATA_ID_DATA_FLAG,&data_flag);
    buff[offset++] = data_flag & 0xFE;
    
    /* WARN_STATE */
    buff[offset++] = 0x01;                    // 温度传感器
    buff[offset++] = temperature_sensor_alarm_judge();
            
    buff[offset++] = 0x01;                    // 湿度传感器
    buff[offset++] = humidity_sensor_alarm_judge();
                        
    buff[offset++] = 0x01;                    // 烟雾传感器
    alm_id = GET_ALM_ID(DCMU_ALM_ID_FUMES_SENSOR_ALARM,1,1);
    alarm_status = get_realtime_alarm_value(alm_id);
    buff[offset++] = alarm_status == TRUE ? ENV_FAULT : 0; 

    buff[offset++] = 0x01;                    // 水浸传感器
    alm_id = GET_ALM_ID(DCMU_ALM_ID_FLOOD_SENSOR_ALARM,1,1);
    alarm_status = get_realtime_alarm_value(alm_id);
    buff[offset++] = alarm_status == TRUE ? ENV_FAULT : 0; 

    buff[offset++] = 0x01;                    // 红外（门禁）传感器告警
    alm_id = GET_ALM_ID(DCMU_ALM_ID_DOORINFRARED_SENSOR_ALARM,1,1);
    alarm_status = get_realtime_alarm_value(alm_id);
    buff[offset++] = alarm_status == TRUE ? ENV_FAULT : 0; 
    
    buff[offset++] = 0x01;                    // 门窗（门磁）传感器告警
    alm_id = GET_ALM_ID(DCMU_ALM_ID_DOORMAT_SENSOR_ALARM,1,1);
    alarm_status = get_realtime_alarm_value(alm_id);
    buff[offset++] = alarm_status == TRUE ? ENV_FAULT : 0; 

    buff[offset++] = 0x01;                    // 玻璃碎传感器告警
    alm_id = GET_ALM_ID(DCMU_ALM_ID_GLASS_SENSOR_ALARM,1,1);
    alarm_status = get_realtime_alarm_value(alm_id);
    buff[offset++] = alarm_status == TRUE ? ENV_FAULT : 0; 
        
    buff[offset++] = 0x00;        // 自定义告警数量为0

    /* LENID */
    cmd_buf_temp->data_len = offset;
    
    return SUCCESSFUL;
}


Static int pack_env_analog_float_data(void* dev_inst, void* cmd_buff) {
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0;
    float f_data = 0.0f;

    buff = cmd_buf_temp->buf;

    /* DATA_FLAG */
    get_one_data(DCMU_DATA_ID_DATA_FLAG, &buff[offset++]);

    /* DATAF */
    buff[offset++] = 0x01;            // 温度传感器数量m=1
    get_one_data(DCMU_DATA_ID_ENV_TEMP, &f_data); // 环境温度
    put_int16_to_buff(&buff[offset], FLOAT_TO_SHORT(f_data));
    put_float_to_buff(&buff[offset], f_data);
    offset += 4;

    buff[offset++] = 0x01;            // 湿度传感器数量n=1
    get_one_data(DCMU_DATA_ID_ENV_HUMIDITY, &f_data); // 环境湿度
    put_float_to_buff(&buff[offset], f_data);
    offset += 4;

    buff[offset] = 0x00;              /* 自定义遥测数量为0 */

    /* LENID */
    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}

Static int pack_dcem_fact_data(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned int offset = 0;
    char dcem_sm_name[30] = DEVICES;
    char dcem_vendor_name[20] = CORPERATION_NAME;
    int i = 0;

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);

    buff = cmd_buf_temp->buf;

    rt_memset_s(&buff[offset], 30, 0x20, 30);
    rt_memcpy_s(&buff[offset], 30, dcem_sm_name, rt_strnlen_s(dcem_sm_name, 30));
    offset += 30;

    buff[offset++] = SOFTWARE_VER1;
    buff[offset++] = SOFTWARE_VER2;

    rt_memset_s(&buff[offset], 20, 0x20, 20);
    rt_memcpy_s(&buff[offset], 20, dcem_vendor_name, rt_strnlen_s(dcem_vendor_name, 20));
    offset += 20;

    ((cmd_buf_t*)cmd_buf)->data_len = offset;
    return SUCCESSFUL;
}



int pack_sys_time_data(void *dev_inst, void *cmd_buff) {

    time_base_t pt_time = {0};
    int offset = 0;
    get_time(&pt_time);

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf;


    put_int16_to_buff(&data_buff[offset], pt_time.year);
    offset += 2;

    data_buff[offset++] = pt_time.month;
    data_buff[offset++] = pt_time.day;
    data_buff[offset++] = pt_time.hour;
    data_buff[offset++] = pt_time.minute;
    data_buff[offset++] = pt_time.second;

    ((cmd_buf_t *)cmd_buff)->data_len = offset;

    return SUCCESSFUL;
}

int parse_sys_time_data(void *dev_inst, void *cmd_buff) {

    time_base_t last_tm = {0},tm = {0};
    char info[20] = {0};
    int offset = 0;

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    time_t tTime = time(RT_NULL);
    time_t_to_timestruct(tTime,&last_tm);

    unsigned char *data_buff = ((cmd_buf_t *)cmd_buff)->buf;

    tm.year = get_int16_data(&data_buff[offset]);
    offset += 2;
    tm.month = data_buff[offset++];
    tm.day = data_buff[offset++];
    tm.hour = data_buff[offset++];
    tm.minute = data_buff[offset++];
    tm.second = data_buff[offset++];

    if( check_time_range(tm) == FAILURE )
    {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA ;
        return SUCCESSFUL;
    }

    /* 设置系统时间 */
    if (set_system_time(&tm) != SUCCESSFUL) {
        ((cmd_buf_t*)cmd_buff)->rtn = RTN_INVALID_DATA;
        return SUCCESSFUL;
    }

    rt_snprintf(info, sizeof(info),
                "%d-%02d-%02d %02d:%02d:%02d", 
               last_tm.year, last_tm.month, last_tm.day, last_tm.hour, last_tm.minute, last_tm.second);
    pub_hisaction_save_msg(ID1_DCMU_SETSTATUS, ID2_PR_TIME, 0, info);

    return SUCCESSFUL;
}



Static int pack_dc_analog_float_data(void* dev_inst, void* cmd_buff) {
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0;
    float float_data = 0;
    int int_data = 0;
    int i = 0;

    buff = cmd_buf_temp->buf;

    /* DATA_FLAG */
    get_one_data(DCMU_DATA_ID_DATA_FLAG, &buff[offset++]);

    /* DATAF */
    buff[offset++] = 0x01;            // 直流屏数量 m=1

    get_one_data(DCMU_DATA_ID_DC_OUTPUT_VOLTAGE, &float_data); // 直流输出电压
    put_float_to_buff(&buff[offset], float_data);
    offset += 4;

    get_one_data(DCMU_DATA_ID_TOTAL_LOAD_CURRENT, &float_data); // 负载总电流
    put_float_to_buff(&buff[offset], float_data);
    offset += 4;

    buff[offset++] = 0x00;            // 监测电池分路电流数量 M=0

    buff[offset++] = SWITCH_NUM;      // 直流分路数量 N=24

    for(i = 0; i < SWITCH_NUM; i++) {
        get_one_data(DCMU_DATA_ID_LOAD_CURRENT + i, &float_data); // 分路电流
        put_float_to_buff(&buff[offset], float_data);
        offset += 4;
    }

    /* LENID */
    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}

Static int pack_dc_switch_data(void* dev_inst, void* cmd_buff) {
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0;
    unsigned char uc_data = 0;
    int i = 0;

    buff = cmd_buf_temp->buf;

    /* DATA_FLAG */
    get_one_data(DCMU_DATA_ID_DATA_FLAG, &buff[offset++]);

    /* DATAF */
    buff[offset++] = 0x01;            // 直流屏数量 m=1
    buff[offset++] = 0x00;            // 状态数量 N=0

    buff[offset++] = RELAY_NUM;       // 输入干接点数量 M=4

    for(i = 0; i < RELAY_NUM; i++) {
        get_one_data(DCMU_DATA_ID_INPUT_RELAY + i, &uc_data); // 输入干接点
        buff[offset++] = uc_data;
    }

    /* LENID */
    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}



Static int pack_dc_para_float(void* dev_inst, void* cmd_buff) {
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0;
    float f_data = 0;

    buff = cmd_buf_temp->buf;

    get_one_para(DCMU_PARA_ID_DC_VOLTAGE_HIGH_THRESHOLD_OFFSET, &f_data);    // 直流电压高阈值
    put_float_to_buff(&buff[offset], f_data);
    offset += 4;

    get_one_para(DCMU_PARA_ID_DC_VOLTAGE_LOW_THRESHOLD_OFFSET, &f_data);     // 直流电压低阈值
    put_float_to_buff(&buff[offset], f_data);
    offset += 4;

    /* LENID */
    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}

Static int parse_dc_para_float(void* dev_inst, void* cmd_buff) {
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char command_type;
    float f_data = 0;
    signed char rst = 0;

    command_type = cmd_buf_temp->buf[0];

    switch (command_type) {
        case 0x80:  // 直流电压高阈值
            f_data = get_float_data(&cmd_buf_temp->buf[1]);
            rst = set_one_para(DCMU_PARA_ID_DC_VOLTAGE_HIGH_THRESHOLD_OFFSET, &f_data, TRUE, FALSE);
            break;
        case 0x81:  // 直流电压低阈值
            f_data = get_float_data(&cmd_buf_temp->buf[1]);
            rst = set_one_para(DCMU_PARA_ID_DC_VOLTAGE_LOW_THRESHOLD_OFFSET, &f_data, TRUE, FALSE);
            break;
        default:
            return FAILURE;
    }
    if(rst < 0)
    {
        cmd_buf_temp->rtn = RTN_WRONG_COMMAND;
    }
    else
    {
        update_para();
    }

    return SUCCESSFUL;
}

Static int pack_env_para_float(void* dev_inst, void* cmd_buff) {
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char* buff;
    unsigned char offset = 0;
    float f_data = 0;
    int i = 0;

    buff = cmd_buf_temp->buf;

    buff[offset++] = TEMPERATURE_SENSOR_NUM;            // 温度传感器数量 m=1
    for(i = 0; i < TEMPERATURE_SENSOR_NUM; i++) {
        get_one_para(DCMU_PARA_ID_ENV_TEMPERATURE_SENSOR_UPPER_LIMIT_OFFSET + i, &f_data);    // 温度传感器上限
        put_float_to_buff(&buff[offset], f_data);
        offset += 4;

        get_one_para(DCMU_PARA_ID_ENV_TEMPERATURE_SENSOR_LOWER_LIMIT_OFFSET + i, &f_data);    // 温度传感器下限
        put_float_to_buff(&buff[offset], f_data);
        offset += 4;
    }

    buff[offset++] = HUMIDITY_SENSOR_NUM;            // 湿度传感器数量 n=1
    for(i = 0; i < HUMIDITY_SENSOR_NUM; i++) {
        get_one_para(DCMU_PARA_ID_ENV_HUMIDITY_SENSOR_UPPER_LIMIT_OFFSET + i, &f_data);    // 湿度传感器上限
        put_float_to_buff(&buff[offset], f_data);
        offset += 4;

        get_one_para(DCMU_PARA_ID_ENV_HUMIDITY_SENSOR_LOWER_LIMIT_OFFSET + i, &f_data);    // 湿度传感器下限
        put_float_to_buff(&buff[offset], f_data);
        offset += 4;
    }

    buff[offset++] = 0;            // 用户自定义参数数量 p=0

    /* LENID */
    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}

Static int parse_env_para_float(void* dev_inst, void* cmd_buff) {
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buff != NULL, FAILURE);

    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buff;
    unsigned char command_type;
    float f_data = 0;
    signed char rst = 0;

    command_type = cmd_buf_temp->buf[0];

    switch (command_type) {
        case 0x80:  // 温度传感器上限
            f_data = get_float_data(&cmd_buf_temp->buf[1]);
            rst = set_one_para(DCMU_PARA_ID_ENV_TEMPERATURE_SENSOR_UPPER_LIMIT_OFFSET, &f_data, TRUE, FALSE);
            break;
        case 0x81:  // 温度传感器下限
            f_data = get_float_data(&cmd_buf_temp->buf[1]);
            rst = set_one_para(DCMU_PARA_ID_ENV_TEMPERATURE_SENSOR_LOWER_LIMIT_OFFSET, &f_data, TRUE, FALSE);
            break;
        case 0x82:  // 湿度传感器上限
            f_data = get_float_data(&cmd_buf_temp->buf[1]);
            rst = set_one_para(DCMU_PARA_ID_ENV_HUMIDITY_SENSOR_UPPER_LIMIT_OFFSET, &f_data, TRUE, FALSE);
            break;
        case 0x83:  // 湿度传感器下限
            f_data = get_float_data(&cmd_buf_temp->buf[1]);
            rst = set_one_para(DCMU_PARA_ID_ENV_HUMIDITY_SENSOR_LOWER_LIMIT_OFFSET, &f_data, TRUE, FALSE);
            break;
        default:
            return FAILURE;
    }

    if(rst < 0)
    {
        cmd_buf_temp->rtn = RTN_WRONG_COMMAND;
    }
    else
    {
        update_para();
    }


    return SUCCESSFUL;
}


