#include "mqttclient.h"
#include "mqtt_main.h"
#include "mqtt_mgr.h"
#include "mqtt_realdata.h"
#include "mqtt_alarm.h"
#include "mqtt_set_get.h"
#include "mqtt_update.h"

#include "thread_id.h"
#include "utils_heart_beat.h"
#include "utils_thread.h"
#include "partition_def.h"
#include "softbus.h"
#include "msg_id.h"
#include "msg.h"
#include "system_manage.h"


static mqtt_client_t* g_zte_client = NULL;
static mqtt_client_t* g_oth_client = NULL;
_rt_server_t g_mqtt_server = NULL;

static cJSON_Hooks g_hooks = {
    .malloc_fn = MQTT_MALLOC,
    .free_fn   = MQTT_FREE
};

static msg_map mqtt_msg_map[] =
{
    {CHECK_SIGNAL_ALARM_CHG_MSG, msg_handle_nothing},
    {MQTT_ALARM_UP_MSG,          msg_handle_nothing},
    {PARALLEL_ALARM_CHANGE_MSG,  msg_handle_nothing},
    {SET_SLAVE_ONE_CMD_RET,      msg_handle_nothing},
    {MQTT_UPDATE_RESULT_MSG,     msg_handle_nothing},
    {MQTT_DEAL_UPDATE_RESULT,    msg_handle_nothing},
    {MQTT_IMMEDIATE_SUBMIT_REAL, msg_handle_nothing},
    {CERT_CHG_MSG,               msg_handle_nothing},
    {MQTT_UPDATE,                msg_handle_nothing},
    {JUDGE_DELAY_UPDATE_STATUS,  msg_handle_nothing},
    {MQTT_REAL_UP_MSG,           msg_handle_nothing},
}; 

static pv_msg_handle_process_t s_msg_handle[] =
{
    {CHECK_SIGNAL_ALARM_CHG_MSG,      handle_accu_alarm_msg},
    {MQTT_ALARM_UP_MSG,               handle_send_alarm_msg},
    {PARALLEL_ALARM_CHANGE_MSG,       handle_send_slave_alarm_msg},
    {SET_SLAVE_ONE_CMD_RET,           handle_send_parallel_set_msg},
    {MQTT_UPDATE_RESULT_MSG,          handle_send_update_msg},
    {MQTT_DEAL_UPDATE_RESULT,         handle_deal_update_res},
    {MQTT_IMMEDIATE_SUBMIT_REAL,      handle_send_immediate_realdata_msg},
    {CERT_CHG_MSG,                    handle_cert_chg_msg},
    {MQTT_UPDATE,                     handle_mqtt_update_msg},
    {JUDGE_DELAY_UPDATE_STATUS,       handle_delay_update_status},
    {MQTT_REAL_UP_MSG,                handle_mqtt_realdata_msg},
};

int mqtt_process_recv_msg(void)
{
    int i = 0;
    if ((g_mqtt_server == NULL) || (rt_sem_take(&g_mqtt_server->msg_sem, 10) != RT_EOK) || (g_mqtt_server->msg_node == RT_NULL))
    {
        return FAILURE;
    }

    _rt_msg_t curr_msg = g_mqtt_server->msg_node;
    for (i = 0; i < sizeof(s_msg_handle) / sizeof(s_msg_handle[0]); i++)
    {
        if (s_msg_handle[i].msg_id == curr_msg->msg.msg_id)
        {
            s_msg_handle[i].handle(curr_msg);
        }
    }

    rt_mutex_take(&g_mqtt_server->mutex, RT_WAITING_FOREVER);
    g_mqtt_server->msg_node = curr_msg->next;
    g_mqtt_server->msg_count--;
    rt_mutex_release(&g_mqtt_server->mutex);

    softbus_free(curr_msg);
    return SUCCESSFUL;
}

void* init_mqtt(void* param)
{   
    set_mqtt_memory_mode(POOL_MEM_MODE);
    create_mqtt_send_alarm_timer();
    create_mqtt_send_real_timer();
    create_net_conn_status_timer();
    create_mqtt_send_update_timer();
    cJSON_InitHooks(&g_hooks);
    server_info_t *server_info = (server_info_t *)param;

    server_info->server.server.map_size = sizeof(mqtt_msg_map) / sizeof(msg_map);
    register_server_msg_map(mqtt_msg_map, server_info);

    g_zte_client = mqtt_lease();
    g_oth_client = mqtt_lease();
    if(g_zte_client == NULL || g_oth_client == NULL)
    {
        LOG_E("init mqtt failure!");
        return NULL;
    }

    set_mqtt_client(ZTE_CLIENT, g_zte_client);
    mqtt_set_use_tls(g_zte_client, RT_TRUE);
    mqtt_set_which_tls_conf(g_zte_client, DEF_TLS);
    mqtt_set_thread_id(g_zte_client, "mqtt_client1");
    mqtt_set_ca_files(g_zte_client, MQTT_CA_FILE, MQTT_CLIENT_FILE, get_client_key_buf());
    mqtt_set_clean_session(g_zte_client, RT_TRUE);
    mqtt_set_connect_handler(g_zte_client, handle_online);
    g_zte_client->mqtt_client_state = CLIENT_STATE_INITIALIZED;

    set_mqtt_client(OTH_CLIENT, g_oth_client);
    mqtt_set_use_tls(g_oth_client, RT_TRUE);
    mqtt_set_which_tls_conf(g_oth_client, OTH_TLS);
    mqtt_set_thread_id(g_oth_client, "mqtt_client2");
    mqtt_set_ca_files(g_oth_client, MQTT_CA_OTHER_FILE, MQTT_CLIENT_OTHER_FILE, get_client_other_key_buf());
    mqtt_set_clean_session(g_oth_client, RT_TRUE);
    mqtt_set_connect_handler(g_oth_client, handle_online);
    g_oth_client->mqtt_client_state = CLIENT_STATE_INITIALIZED;

    init_config_para();
    return g_zte_client;
}

void mqtt_thread_entry(void* thread_data)
{
    PRINT_MSG_AND_RETURN_IF_FAIL(thread_data != NULL);
    g_mqtt_server = _curr_server_get();
    pre_thread_beat_f(THREAD_MQTT);

    while (is_running(TRUE))
    {
        thread_beat_go_on(THREAD_MQTT);
        mqtt_sleep_ms(20);
        mqtt_process_recv_msg();
        if (is_need_close_conn())
        {
            close_client(ZTE_CLIENT);
            close_client(OTH_CLIENT);
            continue;
        }
        manage_connect();
        handle_slave_property();
        judge_master_immediate_to_network();
    }
}
