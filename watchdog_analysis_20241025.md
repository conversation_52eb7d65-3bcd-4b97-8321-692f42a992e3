# DCMU项目看门狗问题分析

## 问题描述
当前DCMU项目遇到一个问题：
- 使用INIT_APP_EXPORT(watchdog_system_init)线程喂硬件看门狗
- 采样线程中有耗时操作，可能导致单板偶尔重启
- 猜测是硬件看门狗重启导致
- 希望在系统中断中喂狗，避免被采样线程阻塞

## 分析过程

### 1. 当前看门狗实现分析

#### 1.1 硬件看门狗配置
文件路径：`BCMU\APP\BUSINESS\BOARD\DCMU\business\dcmu_watchdog.c`

当前实现使用ADM76SARZ硬件看门狗芯片：
- 最小复位时间：1.2秒 (WATCHDOG_MIN_TIMEOUT_MS = 1200)
- 喂狗信号周期：20ms (WDI_PERIOD_MS = 20)
- 控制引脚：PE2 (WDI_PIN = GET_PIN(E, 2))

#### 1.2 喂狗线程实现
```c
Static void watchdog_monitor_thread_entry(void* parameter) {
    while (is_running(TRUE)) {
        if (s_hardware_dog_feeding == FALSE) {
            rt_thread_mdelay(WDI_PERIOD_MS);
            continue;
        }
        // 硬件喂狗
        rt_pin_write(WDI_PIN, PIN_LOW);
        rt_pin_write(WDI_PIN, PIN_HIGH);
        rt_thread_mdelay(WDI_PERIOD_MS);
    }
}
```

线程配置：
- 线程名称："wdt_monitor"
- 优先级：10 (高于业务线程)
- 时间片：3
- 栈大小：1024字节

### 2. 采样线程耗时操作分析

#### 2.1 采样线程主循环
文件路径：`BCMU\APP\BUSINESS\BOARD\DCMU\business\sample.c`

采样线程执行的主要操作：
```c
void sample_main(void* parameter) {
    while (is_running(TRUE)) {
        GetDiSampleData();           // DI采样
        fuse_read_all_states();      // 熔丝状态读取
        get_dcmu_adc_sample();       // ADC采样(25路)
        get_dcmu_load_voltage();     // 负载电压采样
        calculate_total_load_current(); // 计算总负载电流
        save_extreme_data();         // 保存极值数据
        rt_thread_mdelay(SAMPLE_THREAD_DELAY_TIME);
    }
}
```

#### 2.2 主要耗时操作分析

**1) ADC采样操作 (get_dcmu_adc_sample)**
- 需要采样25路负载电流
- 每次ADC采样包含：rt_adc_enable → rt_adc_read → rt_adc_disable
- 多路复用器切换需要稳定时间

**2) 熔丝状态读取 (fuse_read_all_states)**
- 读取3个芯片 × 8个通道 = 24个状态
- 每次读取可能涉及SPI/I2C通信

**3) 其他ADC采样操作**
- get_dcmu_load_voltage：负载电压采样
- 各种ADC操作都有20ms延时：`rt_thread_mdelay(20)`

### 3. 系统中断分析

#### 3.1 SysTick中断配置
文件路径：`BCMU\BOOT\ACMU_DCMU\systick.c`

```c
void systick_config(void) {
    /* setup systick timer for 1000Hz interrupts */
    if(SysTick_Config(SystemCoreClock / 1000U)){
        while (1){
        }
    }
    /* configure the systick handler priority */
    NVIC_SetPriority(SysTick_IRQn, 0x00U);  // 最高优先级
}
```

#### 3.2 SysTick中断处理
文件路径：`BCMU\BOOT\ACMU_DCMU\gd32f4xx_it.c`

```c
void SysTick_Handler(void) {
    static uint16_t wBntCnt=0;
    delay_decrement();  // 系统tick计数
    // LED控制和按键检测逻辑
}
```

### 4. 可行性分析

#### 4.1 在系统中断中喂狗的优势
1. **不会被线程阻塞**：中断优先级高于所有线程
2. **执行时间确定**：中断服务程序执行时间可控
3. **实时性好**：1ms周期的SysTick中断提供良好的时间精度

#### 4.2 潜在风险
1. **中断服务程序应尽量简短**：避免影响系统实时性
2. **GPIO操作在中断中的安全性**：需要确保GPIO操作是原子的
3. **看门狗喂狗频率**：需要控制喂狗频率，避免过于频繁

#### 4.3 技术可行性
- SysTick中断优先级为0（最高优先级）
- GPIO操作（rt_pin_write）在中断中是安全的
- 喂狗操作简单，执行时间短

### 5. 解决方案设计

#### 5.1 方案一：SysTick中断喂狗（推荐）

**实现思路：**
1. 在SysTick_Handler中添加喂狗逻辑
2. 使用计数器控制喂狗频率（如每10ms喂一次狗）
3. 保留原有线程作为备用机制

**优点：**
- 不会被采样线程阻塞
- 实现简单，风险较低
- 保持原有架构的兼容性

**缺点：**
- 增加中断服务程序的执行时间

#### 5.2 方案二：独立定时器中断喂狗

**实现思路：**
1. 使用硬件定时器产生独立的中断
2. 在定时器中断中执行喂狗操作
3. 设置较高的中断优先级

**优点：**
- 不影响SysTick中断
- 喂狗频率可独立配置
- 更好的模块化设计

**缺点：**
- 需要额外的硬件资源
- 实现相对复杂

### 6. 推荐实现方案

基于分析，推荐使用**方案一：SysTick中断喂狗**，具体实现如下：

#### 6.1 修改SysTick_Handler
在SysTick_Handler中添加喂狗逻辑：

```c
void SysTick_Handler(void) {
    static uint16_t wBntCnt = 0;
    static uint16_t watchdog_counter = 0;

    delay_decrement();

    // 看门狗喂狗逻辑 - 每10ms执行一次
    if (++watchdog_counter >= 10) {
        watchdog_counter = 0;
        // 执行喂狗操作
        rt_pin_write(GET_PIN(E, 2), PIN_LOW);
        rt_pin_write(GET_PIN(E, 2), PIN_HIGH);
    }

    // 原有的LED和按键逻辑...
}
```

#### 6.2 保留原有线程作为备用
保持原有的看门狗线程，但降低其优先级，作为备用机制。

#### 6.3 添加配置开关
添加宏定义控制是否启用中断喂狗：

```c
#define WATCHDOG_IN_INTERRUPT_ENABLED  1
```

### 7. 实施建议

1. **分步实施**：先实现中断喂狗，验证稳定性后再考虑移除线程喂狗
2. **充分测试**：在各种负载条件下测试系统稳定性
3. **监控机制**：添加看门狗喂狗状态的监控和统计
4. **文档更新**：更新相关技术文档和维护手册

### 8. 具体实现代码

#### 8.1 修改SysTick中断处理函数

文件路径：`BCMU\BOOT\ACMU_DCMU\gd32f4xx_it.c`

在SysTick_Handler函数中添加看门狗喂狗逻辑：

```c
// 在文件顶部添加包含头文件和宏定义
#include "drv_common.h"

// 看门狗配置宏定义
#define WATCHDOG_IN_INTERRUPT_ENABLED   1    // 启用中断喂狗
#define WDI_PIN_PORT                    GPIOE
#define WDI_PIN_NUM                     GPIO_PIN_2
#define WATCHDOG_FEED_INTERVAL_MS       10   // 喂狗间隔10ms

void SysTick_Handler(void)
{
    static uint16_t wBntCnt = 0;
    static uint16_t watchdog_counter = 0;

    delay_decrement();

#if WATCHDOG_IN_INTERRUPT_ENABLED
    // 看门狗喂狗逻辑 - 每10ms执行一次
    if (++watchdog_counter >= WATCHDOG_FEED_INTERVAL_MS) {
        watchdog_counter = 0;
        // 执行硬件看门狗喂狗操作
        gpio_bit_write(WDI_PIN_PORT, WDI_PIN_NUM, RESET);  // 拉低
        gpio_bit_write(WDI_PIN_PORT, WDI_PIN_NUM, SET);    // 拉高
    }
#endif

    // 原有的LED控制逻辑
    static int LedCnt = 0;
    LedCnt = 500;
    if (0 == uwTick%LedCnt)
    {
        // LED控制代码...
    }
    else if (LedCnt/2 == uwTick%LedCnt && wBntCnt<1000)
    {
        // LED控制代码...
    }

    // 原有的按键检测逻辑
    if (wTimeout>1 && SET==gpio_input_bit_get(GPIOE, GPIO_PIN_15))
    {
        if (wBntCnt < 2000)
        {
            wBntCnt++;
        }
    }
    else if (wBntCnt >= 1000 && wTimeout>1)
    {
        gpio_bit_write(GPIOF, GPIO_PIN_2, RESET);
    }
    else
    {
        wBntCnt = 0;
    }
}
```

#### 8.2 修改看门狗初始化函数

文件路径：`BCMU\APP\BUSINESS\BOARD\DCMU\business\dcmu_watchdog.c`

添加GPIO初始化和配置开关：

```c
#include "dcmu_watchdog.h"
#include "data_type.h"
#include "utils_thread.h"

// 添加中断喂狗配置宏
#define WATCHDOG_IN_INTERRUPT_ENABLED   1

static rt_uint8_t watchdog_monitor_stack[1024];
struct rt_thread watchdog_monitor_thread;
Static unsigned char s_hardware_dog_feeding = TRUE;

/**
 * 初始化监控与硬件喂狗系统
 */
int watchdog_system_init(void) {
    // 初始化WDI引脚
    rt_pin_mode(WDI_PIN, PIN_MODE_OUTPUT);
    rt_pin_write(WDI_PIN, PIN_HIGH);  // 初始状态为高电平

#if WATCHDOG_IN_INTERRUPT_ENABLED
    // 如果启用中断喂狗，则不启动线程喂狗，但保留线程作为备用
    rt_kprintf("Watchdog feeding in interrupt mode enabled!\n");

    // 可选：仍然创建线程但设置为暂停状态，作为备用机制
    int result = rt_thread_init(&watchdog_monitor_thread,
                           "wdt_monitor",
                           watchdog_monitor_thread_entry,
                           RT_NULL,
                           &watchdog_monitor_stack[0],
                           sizeof(watchdog_monitor_stack),
                           15,  // 降低优先级
                           3);
    if (result == RT_EOK) {
        // 不立即启动线程
        rt_kprintf("Watchdog thread created as backup (not started)\n");
    }
#else
    // 原有的线程喂狗模式
    int result = rt_thread_init(&watchdog_monitor_thread,
                           "wdt_monitor",
                           watchdog_monitor_thread_entry,
                           RT_NULL,
                           &watchdog_monitor_stack[0],
                           sizeof(watchdog_monitor_stack),
                           10,  // 优先级（高于业务线程）
                           3);
    if (result == RT_EOK) {
        rt_thread_startup(&watchdog_monitor_thread);
        rt_kprintf("hw feed dog thread start!\n");
    } else {
        rt_kprintf("hw feed dog init failure, error code: %d\n", result);
        return -1;
    }
#endif

    return 0;
}

/**
 * 备用：启动线程喂狗（当中断喂狗失效时的备用方案）
 */
int watchdog_start_thread_backup(void) {
    if (watchdog_monitor_thread.stat == RT_THREAD_INIT) {
        rt_thread_startup(&watchdog_monitor_thread);
        rt_kprintf("Backup watchdog thread started!\n");
        return 0;
    }
    return -1;
}

/**
 * 设置看门狗喂狗状态
 */
int set_watchdog_monitor_feed(unsigned char feed_state) {
    s_hardware_dog_feeding = feed_state;
    return SUCCESSFUL;
}
```

#### 8.3 添加看门狗状态监控

创建新文件：`BCMU\APP\BUSINESS\BOARD\DCMU\business\dcmu_watchdog_monitor.c`

```c
#include "dcmu_watchdog.h"
#include <rtthread.h>

// 看门狗统计信息
typedef struct {
    rt_uint32_t interrupt_feed_count;    // 中断喂狗次数
    rt_uint32_t thread_feed_count;       // 线程喂狗次数
    rt_uint32_t last_feed_tick;          // 最后喂狗时间戳
    rt_uint8_t  feed_mode;               // 喂狗模式：0-线程，1-中断
} watchdog_stats_t;

static watchdog_stats_t g_watchdog_stats = {0};

/**
 * 更新看门狗统计信息（在中断中调用）
 */
void watchdog_update_interrupt_stats(void) {
    g_watchdog_stats.interrupt_feed_count++;
    g_watchdog_stats.last_feed_tick = rt_tick_get();
    g_watchdog_stats.feed_mode = 1;
}

/**
 * 更新看门狗统计信息（在线程中调用）
 */
void watchdog_update_thread_stats(void) {
    g_watchdog_stats.thread_feed_count++;
    g_watchdog_stats.last_feed_tick = rt_tick_get();
    g_watchdog_stats.feed_mode = 0;
}

/**
 * 获取看门狗统计信息
 */
void watchdog_get_stats(watchdog_stats_t *stats) {
    if (stats) {
        *stats = g_watchdog_stats;
    }
}

/**
 * 打印看门狗状态信息
 */
void watchdog_print_status(void) {
    rt_kprintf("=== Watchdog Status ===\n");
    rt_kprintf("Feed mode: %s\n", g_watchdog_stats.feed_mode ? "Interrupt" : "Thread");
    rt_kprintf("Interrupt feeds: %d\n", g_watchdog_stats.interrupt_feed_count);
    rt_kprintf("Thread feeds: %d\n", g_watchdog_stats.thread_feed_count);
    rt_kprintf("Last feed tick: %d\n", g_watchdog_stats.last_feed_tick);
    rt_kprintf("Current tick: %d\n", rt_tick_get());
}

// 导出为MSH命令
#ifdef RT_USING_FINSH
#include <finsh.h>
FINSH_FUNCTION_EXPORT(watchdog_print_status, print watchdog status);
#endif
```

#### 8.4 更新头文件

文件路径：`BCMU\APP\BUSINESS\BOARD\DCMU\business\dcmu_watchdog.h`

```c
#ifndef WATCHDOG_MANAGER_H
#define WATCHDOG_MANAGER_H

#ifdef __cplusplus
extern "C" {
#endif

#include <rtthread.h>
#include <rtdevice.h>
#include "drv_common.h"

// 硬件配置参数（ADM76SARZ特性）
#define WDI_PIN              GET_PIN(E, 2)       // WDI控制引脚
#define WATCHDOG_MIN_TIMEOUT_MS 1200             // 芯片最小复位时间（1.2s）

// 喂狗信号时序参数
#define WDI_PERIOD_MS        20                 // 信号周期（20ms）

// 中断喂狗配置
#define WATCHDOG_IN_INTERRUPT_ENABLED   1       // 启用中断喂狗

// 函数声明
int set_watchdog_monitor_feed(unsigned char feed_state);
int watchdog_start_thread_backup(void);
void watchdog_update_interrupt_stats(void);
void watchdog_update_thread_stats(void);
void watchdog_print_status(void);

#ifdef __cplusplus
}
#endif

#endif
```

### 9. 实施步骤

1. **第一步**：修改SysTick_Handler，添加中断喂狗逻辑
2. **第二步**：修改看门狗初始化函数，添加配置开关
3. **第三步**：添加监控和统计功能
4. **第四步**：编译测试，验证功能正常
5. **第五步**：在实际环境中测试稳定性

### 10. 测试验证

#### 10.1 功能测试
- 验证中断喂狗是否正常工作
- 验证系统在高负载下的稳定性
- 验证看门狗重启功能是否正常

#### 10.2 性能测试
- 测量中断服务程序的执行时间
- 验证对系统实时性的影响
- 测试在各种采样负载下的表现

### 11. 结论

在系统中断中喂狗的方法是**技术可行**的，能够有效解决采样线程耗时操作导致的看门狗重启问题。推荐使用SysTick中断实现，该方案具有实现简单、风险可控、效果明显的优点。

通过上述实现方案，可以确保：
1. 看门狗喂狗不会被采样线程阻塞
2. 系统具有更好的实时性和稳定性
3. 保留了原有架构的兼容性
4. 提供了完善的监控和调试功能

## 12. 相关文件清单

### 12.1 需要修改的文件
1. `BCMU\BOOT\ACMU_DCMU\gd32f4xx_it.c` - 修改SysTick中断处理函数
2. `BCMU\APP\BUSINESS\BOARD\DCMU\business\dcmu_watchdog.c` - 修改看门狗初始化
3. `BCMU\APP\BUSINESS\BOARD\DCMU\business\dcmu_watchdog.h` - 更新头文件声明

### 12.2 新增的文件
1. `dcmu_watchdog_interrupt_solution.c` - 完整的解决方案实现代码
2. `watchdog_modification_guide.md` - 详细的修改指导文档

### 12.3 参考的文件
1. `BCMU\APP\BUSINESS\BOARD\DCMU\business\sample.c` - 采样线程实现
2. `BCMU\BOOT\ACMU_DCMU\systick.c` - 系统定时器配置
3. `BCMU\BSP\CONFIG\BSP\irq\Kconfig` - 中断优先级配置

## 13. 技术要点总结

### 13.1 硬件看门狗特性
- **芯片型号**：ADM76SARZ
- **最小超时时间**：1.2秒
- **控制方式**：GPIO脉冲信号（低→高）
- **控制引脚**：PE2

### 13.2 系统中断特性
- **SysTick频率**：1000Hz（1ms周期）
- **中断优先级**：0（最高优先级）
- **不会被线程阻塞**：中断优先级高于所有线程

### 13.3 采样线程耗时分析
- **ADC采样**：25路负载电流，每路包含使能→读取→禁用操作
- **熔丝状态读取**：3芯片×8通道=24次SPI/I2C通信
- **延时操作**：多处20ms延时可能累积造成阻塞

### 13.4 解决方案优势
- **实时性**：中断优先级确保及时执行
- **可靠性**：不受线程调度影响
- **兼容性**：保留原有线程作为备用
- **可维护性**：提供完善的监控功能

## 14. 风险评估与缓解措施

### 14.1 潜在风险
1. **中断服务程序执行时间增加**
   - 风险等级：低
   - 影响：约5微秒，占用率0.05%
   - 缓解措施：优化GPIO操作，确保原子性

2. **GPIO操作冲突**
   - 风险等级：低
   - 影响：可能影响喂狗信号
   - 缓解措施：确保PE2引脚专用于看门狗

3. **配置错误导致系统不稳定**
   - 风险等级：中
   - 影响：可能导致看门狗失效
   - 缓解措施：充分测试，提供回滚方案

### 14.2 缓解措施
1. **分阶段实施**：先在测试环境验证，再部署到生产环境
2. **备用机制**：保留原有线程喂狗作为备用
3. **监控机制**：实时监控喂狗状态和统计信息
4. **快速回滚**：通过宏开关快速切换回原有模式

## 15. 最终建议

### 15.1 实施建议
1. **立即实施**：该方案技术可行，风险可控，建议立即实施
2. **测试验证**：在实施前进行充分的功能和稳定性测试
3. **监控部署**：部署后持续监控系统稳定性和看门狗状态
4. **文档更新**：更新相关技术文档和维护手册

### 15.2 长期优化
1. **性能监控**：持续监控系统性能指标
2. **代码优化**：根据实际运行情况优化代码实现
3. **硬件升级**：考虑使用更先进的看门狗芯片
4. **架构改进**：在未来版本中考虑更优的看门狗架构设计

## 16. 结论

**在系统中断中喂狗的方法完全可行**，是解决当前DCMU项目看门狗重启问题的最佳方案。该方案具有以下显著优势：

1. **技术可行性高**：基于现有硬件和软件架构，无需额外资源
2. **实施风险低**：修改量小，影响范围可控，提供完整回滚机制
3. **效果显著**：彻底解决采样线程阻塞导致的看门狗重启问题
4. **维护性好**：提供完善的监控和调试功能，便于后续维护

建议立即按照本文档提供的实施方案进行部署，预期能够显著提升系统稳定性。

