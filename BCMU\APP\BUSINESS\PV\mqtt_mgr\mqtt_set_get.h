#ifndef _MQTTT_SET_GET_H_
#define _MQTTT_SET_GET_H_

#ifdef __cplusplus
extern "C" {
#endif    //  __cplusplus

#include "mqttclient.h"

int handle_set_data(void* client, message_data_t* msg);
int handle_get_data(void* client, message_data_t* msg);
void handle_send_parallel_set_msg(_rt_msg_t curr_msg);
int is_get_slave_data_ok(char dev_addr);
cJSON* handle_get_data_arr(int is_mst, char dev_addr, cJSON* recv_arr, int id_num);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif // _MQTTT_SET_GET_H_
