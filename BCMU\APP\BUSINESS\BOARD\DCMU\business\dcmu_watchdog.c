#include "dcmu_watchdog.h"
#include "data_type.h"
#include "utils_thread.h"

// 看门狗中断喂狗配置
#define WATCHDOG_IN_INTERRUPT_ENABLED   0    // 禁用中断喂狗，先恢复线程模式

static rt_uint8_t watchdog_monitor_stack[1024];
struct rt_thread watchdog_monitor_thread;
Static unsigned char s_hardware_dog_feeding = TRUE;

/**
 * 喂狗监控线程：基于绝对时间差的核心控制逻辑
 */
Static void watchdog_monitor_thread_entry(void* parameter) {
    while (is_running(TRUE)) {
        if (s_hardware_dog_feeding == FALSE) {
            rt_thread_mdelay(WDI_PERIOD_MS);
            continue;
        }
        // 硬件喂狗
        rt_pin_write(WDI_PIN, PIN_LOW);
        rt_pin_write(WDI_PIN, PIN_HIGH);
 
        rt_thread_mdelay(WDI_PERIOD_MS);
    }
}

/**
 * 初始化监控与硬件喂狗系统（静态创建）
 */
int watchdog_system_init(void) {
    // 注意：PE2引脚已经在Boot阶段的main.c中通过USE_DCMU_HARD宏初始化过了
    // 这里不需要重复初始化，避免冲突
    // rt_pin_mode(WDI_PIN, PIN_MODE_OUTPUT);  // 已在Boot中初始化
    // rt_pin_write(WDI_PIN, PIN_HIGH);        // 已在Boot中设置为高电平

#if WATCHDOG_IN_INTERRUPT_ENABLED
    // 如果启用中断喂狗，则不启动线程喂狗，但保留线程作为备用
    rt_kprintf("Watchdog feeding in interrupt mode enabled!\n");

    // 创建线程但不启动，作为备用机制
    int result = rt_thread_init(&watchdog_monitor_thread,
                           "wdt_monitor",
                           watchdog_monitor_thread_entry,
                           RT_NULL,
                           &watchdog_monitor_stack[0],
                           sizeof(watchdog_monitor_stack),
                           15,  // 降低优先级，作为备用
                           3);
    if (result == RT_EOK) {
        rt_kprintf("Watchdog thread created as backup (not started)\n");
    } else {
        rt_kprintf("Watchdog backup thread init failure, error code: %d\n", result);
    }
#else
    // 原有的线程喂狗模式
    int result = rt_thread_init(&watchdog_monitor_thread,
                           "wdt_monitor",
                           watchdog_monitor_thread_entry,
                           RT_NULL,
                           &watchdog_monitor_stack[0],
                           sizeof(watchdog_monitor_stack),
                           10,  // 优先级（高于业务线程）
                           3); // 时间片
    if (result == RT_EOK) {
        rt_thread_startup(&watchdog_monitor_thread);
        rt_kprintf("hw feed dog thread start!\n");
    } else {
        rt_kprintf("hw feed dog init failure, error code: %d\n", result);
        return -1;
    }
#endif

    return 0;
}

int set_watchdog_monitor_feed(unsigned char feed_state) {
    s_hardware_dog_feeding = feed_state;
    return SUCCESSFUL;
}

/**
 * 备用：启动线程喂狗（当中断喂狗失效时的备用方案）
 */
int watchdog_start_thread_backup(void) {
    if (watchdog_monitor_thread.stat == RT_THREAD_INIT) {
        rt_thread_startup(&watchdog_monitor_thread);
        rt_kprintf("Backup watchdog thread started!\n");
        return 0;
    }
    rt_kprintf("Backup watchdog thread already running or not initialized!\n");
    return -1;
}

/**
 * 停止备用线程喂狗
 */
int watchdog_stop_thread_backup(void) {
    if (watchdog_monitor_thread.stat != RT_THREAD_INIT) {
        rt_thread_detach(&watchdog_monitor_thread);
        rt_kprintf("Backup watchdog thread stopped!\n");
        return 0;
    }
    return -1;
}

/**
 * 打印看门狗状态信息
 */
void watchdog_print_status(void) {
    rt_kprintf("=== DCMU Watchdog Status ===\n");
#if WATCHDOG_IN_INTERRUPT_ENABLED
    rt_kprintf("Feed mode: Interrupt (SysTick)\n");
    rt_kprintf("Feed interval: 10ms\n");
    rt_kprintf("Thread status: Backup (not started)\n");
#else
    rt_kprintf("Feed mode: Thread\n");
    rt_kprintf("Feed interval: 20ms\n");
    rt_kprintf("Thread status: Running\n");
#endif
    rt_kprintf("Hardware feeding: %s\n", s_hardware_dog_feeding ? "Enabled" : "Disabled");
    rt_kprintf("WDI Pin: PE2\n");
    rt_kprintf("Watchdog timeout: 1.2s\n");
}

// 导出MSH命令用于调试
#ifdef RT_USING_FINSH
#include <finsh.h>
FINSH_FUNCTION_EXPORT(watchdog_print_status, print watchdog status);
FINSH_FUNCTION_EXPORT(watchdog_start_thread_backup, start backup thread);
FINSH_FUNCTION_EXPORT(watchdog_stop_thread_backup, stop backup thread);
#endif

// 注册到RT-Thread初始化流程
INIT_APP_EXPORT(watchdog_system_init);