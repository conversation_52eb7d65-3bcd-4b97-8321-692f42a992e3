#include "dcmu_watchdog.h"
#include "data_type.h"
#include "utils_thread.h"

static rt_uint8_t watchdog_monitor_stack[1024];
struct rt_thread watchdog_monitor_thread;
Static unsigned char s_hardware_dog_feeding = TRUE;

/**
 * 喂狗监控线程：基于绝对时间差的核心控制逻辑
 */
Static void watchdog_monitor_thread_entry(void* parameter) {
    while (is_running(TRUE)) {
        if (s_hardware_dog_feeding == FALSE) {
            rt_thread_mdelay(WDI_PERIOD_MS);
            continue;
        }
        // 硬件喂狗
        rt_pin_write(WDI_PIN, PIN_LOW);
        rt_pin_write(WDI_PIN, PIN_HIGH);
 
        rt_thread_mdelay(WDI_PERIOD_MS);
    }
}

/**
 * 初始化监控与硬件喂狗系统（静态创建）
 */
int watchdog_system_init(void) {
    // rt_pin_mode(WDI_PIN, PIN_MODE_OUTPUT);
    // 初始化并启动静态监控线程
    int result = rt_thread_init(&watchdog_monitor_thread,
                           "wdt_monitor",
                           watchdog_monitor_thread_entry,
                           RT_NULL,
                           &watchdog_monitor_stack[0],
                           sizeof(watchdog_monitor_stack),
                           10,  // 优先级（高于业务线程）
                           3); // 时间片
    if (result == RT_EOK) {
        rt_thread_startup(&watchdog_monitor_thread);
        rt_kprintf("hw feed dog start!\n");
    } else {
        rt_kprintf("hw feed dog init failure, error code: %d\n", result);
        return -1;
    }

    return 0;
}

int set_watchdog_monitor_feed(unsigned char feed_state) {
    s_hardware_dog_feeding = feed_state;
    return SUCCESSFUL;
}

// 注册到RT-Thread初始化流程
INIT_APP_EXPORT(watchdog_system_init);