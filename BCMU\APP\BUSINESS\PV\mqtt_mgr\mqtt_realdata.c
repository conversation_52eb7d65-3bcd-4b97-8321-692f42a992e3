#include "mqtt_realdata.h"
#include "mqtt_utils.h"

#include "server_id.h"
#include "msg.h"
#include "msg_id.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "north_parallel_modbus.h"
#include "utils_rtthread_security_func.h"
#include "mqtt_alarm.h"

#define FLT_EPSILON 1e-5
#define FLOAT_EQUAL(a, b) (fabs((a) - (b)) < FLT_EPSILON)

static rt_timer_t g_real_timer = NULL;
static char s_need_get_slave_data = TRUE;

// 通过计数发送,5min调用一次
void send_mqtt_realdata(void *parameter)
{
    int idx = 0;
    if (parameter != NULL)
    {
        send_master_property(parameter);
    }
    else
    {
        for (idx = 0; idx < MAX_CLIENT; idx++)
        {
            send_master_property(get_mqtt_client(idx));
        }
    }

    s_need_get_slave_data = TRUE;
}

void send_mqtt_realdata_msg(void *parameter)
{
    send_msg_to_thread(MQTT_REAL_UP_MSG, MOD_MQTT, NULL, 0);
}

void handle_mqtt_realdata_msg(_rt_msg_t curr_msg)
{
    send_mqtt_realdata(NULL);
    handle_send_all_alarm();
}

int create_mqtt_send_real_timer(void)
{
    char* timer_name = "mqtt_real_timer";

    g_real_timer = rt_timer_create(timer_name, send_mqtt_realdata_msg, NULL, TIMER_5MIN, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    if (g_real_timer == NULL)
    {
        LOG_E("mqtt_real_timer fail\n");
        return FAILURE;
    }

    rt_timer_start(g_real_timer);
    return SUCCESSFUL;
}

// 通讯断的设备只用发送离线，不用获取实时数据和更新设备信息
int update_cur_dev_info(unsigned char dev_addr)
{
    static short s_has_sent = 0;
    char sn[SN_LEN] = {0};
    char slave_sn[SN_LEN] = {0};
    char if_find = FALSE;
    short online_dev = get_parallel_status()->online_dev;
    RETURN_VAL_IF_FAIL(dev_addr != get_master_addr(), TRUE);
    get_serial_number(dev_addr, slave_sn, SN_LEN);

    // 通讯断
    if (!GET_DEV_INFO(online_dev, dev_addr - 1))
    {
        // 未发送离线
        if (!GET_DEV_INFO(s_has_sent, dev_addr - 1))
        {
            set_device_id(dev_addr - 1, slave_sn, SN_LEN);
            send_on_offline(get_mqtt_client(ZTE_CLIENT), dev_addr - 1, OFFLINE);
            send_on_offline(get_mqtt_client(OTH_CLIENT), dev_addr - 1, OFFLINE);
            s_has_sent |= 1 << (dev_addr - 1);
        }
        return FALSE;
    } // 通讯正常， 发过离线
    else if (GET_DEV_INFO(s_has_sent, dev_addr - 1))
    {
        send_on_offline(get_mqtt_client(ZTE_CLIENT), dev_addr - 1, ONLINE);
        send_on_offline(get_mqtt_client(OTH_CLIENT), dev_addr - 1, ONLINE);
        s_has_sent &= ~(1 << (dev_addr - 1));
        return TRUE;
    }

    // 设备地址可能修改，需更新设备条码，不用发送离线
    for (int idx = 0; idx < PV_NUM; idx++)
    {
        get_device_id(idx, sn);
        if (rt_strncmp(sn, slave_sn, SN_LEN) == 0)
        {
            if_find = TRUE;
            if (idx != (dev_addr - 1))
            {
                set_device_id(idx, "", 0);
                set_device_id(dev_addr - 1, slave_sn, SN_LEN);
            }
        }
    }

    // 设备如果更换，需更新设备条码，并发送就设备的离线信息和新设备的上线，未更换则直接上线
    if (!if_find)
    {
        get_device_id(dev_addr - 1, sn);
        if (rt_strnlen(sn, SN_LEN) == SN_LEN)
        {
            send_on_offline(get_mqtt_client(ZTE_CLIENT), dev_addr - 1, OFFLINE);
            send_on_offline(get_mqtt_client(OTH_CLIENT), dev_addr - 1, OFFLINE);
        }
        set_device_id(dev_addr - 1, slave_sn, SN_LEN);
        send_on_offline(get_mqtt_client(ZTE_CLIENT), dev_addr - 1, ONLINE);
        send_on_offline(get_mqtt_client(OTH_CLIENT), dev_addr - 1, ONLINE);
    }
    return TRUE;
}

void send_master_property(mqtt_client_t* client)
{
    char topic[MQTT_TOPIC_LEN_MAX] = {0};
    char uuid[UUID_LEN] = {0};
    char site_id[SITE_ID_LEN] = {0};
    char device_id[STR_LEN_16] = {0};
    cJSON* arr = NULL;
    cJSON* send_json = NULL;
    int which = 0;
    if (client->mqtt_client_state != CLIENT_STATE_CONNECTED)
    {
        return;
    }

    send_json = cJSON_CreateObject();
    if (send_json == NULL)
    {
        LOG_E("send_master_property cJSON_CreateObject failed");
        return;
    }

    which = which_client(client);
    gen_uuid(uuid);
    get_site_id(which, site_id);
    get_device_id(get_master_addr() - 1, device_id);
    cJSON_AddStringToObject(send_json, "msgId", uuid);
    cJSON_AddNumberToObject(send_json, "time", mqtt_get_timestamp());

    rt_snprintf(topic, MQTT_TOPIC_LEN_MAX, "/zte/up%s/inv/%s/property", site_id, device_id);

    while ((arr = pack_property(TRUE, get_master_addr(), FALSE)) != NULL)
    {
        cJSON_AddItemToObject(send_json, PROPERTIES_OBJ, arr);
        send_mqtt_data(client, topic, send_json);
        cJSON_DeleteItemFromObject(send_json, PROPERTIES_OBJ);
    }
    cJSON_Delete(send_json);
}

int send_on_offline(mqtt_client_t* client, int dev_idx, int opt)
{
    char topic[MQTT_TOPIC_LEN_MAX] = {0};
    char uuid[UUID_LEN] = {0};
    char site_id[SITE_ID_LEN] = {0};
    char device_id[STR_LEN_16] = {0};
    cJSON* json_data = NULL;
    int which = 0;

    get_device_id(dev_idx, device_id);
    if (rt_strnlen(device_id, SN_LEN) != SN_LEN)
    {
        return MQTT_NULL_ERR;
    }
    json_data = cJSON_CreateObject();
    if (json_data == NULL)
    {
        LOG_E("send_on_offline create json error!\n");
        return MQTT_NULL_ERR;
    }

    which = which_client(client);
    gen_uuid(uuid);
    get_site_id(which, site_id);
    rt_snprintf(topic, MQTT_TOPIC_LEN_MAX, "/zte/up%s/inv/%s/online", site_id, device_id);
    cJSON_AddStringToObject(json_data, "msgId", uuid);
    cJSON_AddNumberToObject(json_data, "time", mqtt_get_timestamp());
    cJSON_AddNumberToObject(json_data, "status", opt);
    send_mqtt_data(client, topic, json_data);

    cJSON_Delete(json_data);
    return MQTT_SUCCESS;
}

int handle_cur_slave_realdata_over(char dev_addr, char is_immediate_submit)
{
    char sn[STR_LEN_16] = {0};
    char uuid[UUID_LEN] = {0};
    char site_id[SITE_ID_LEN] = {0};
    char topic[MQTT_TOPIC_LEN_MAX] = {0};
    cJSON* send_json = NULL;
    cJSON* arr = NULL;

    // 设备通讯异常，直接跳过该设备
    if (update_cur_dev_info(dev_addr) == FALSE)
    {
        return TRUE;
    }
    
    gen_uuid(uuid);
    get_device_id(dev_addr - 1, sn);
    if(rt_strnlen_s(sn, STR_LEN_16) == 0)
    {
        // rt_kprintf("not get addr:%d realdata\n", dev_addr);
        return FALSE;
    }

    send_json = cJSON_CreateObject();
    if (send_json == NULL)
    {
        LOG_E("slave %d cJSON_CreateObject failed", dev_addr);
        return FALSE;
    }
    cJSON_AddStringToObject(send_json, "msgId", uuid);
    cJSON_AddNumberToObject(send_json, "time", mqtt_get_timestamp());
    
    
    while ((arr = pack_property(dev_addr == get_master_addr(), dev_addr, is_immediate_submit)) != NULL)
    {
        cJSON_AddItemToObject(send_json, PROPERTIES_OBJ, arr);
        for (int idx = 0; idx < MAX_CLIENT; idx++)
        {   
            get_site_id(idx, site_id);
            rt_snprintf(topic, MQTT_TOPIC_LEN_MAX, "/zte/up%s/inv/%s/property", site_id, sn);
            mqtt_client_t* client = get_mqtt_client(idx);
            send_mqtt_data(client, topic, send_json);
        }
        cJSON_DeleteItemFromObject(send_json, PROPERTIES_OBJ);
    }
    cJSON_Delete(send_json);
    return TRUE;
}

int handle_slave_property(void)
{
    static char dev_addr = 1;
    short valid_dev = 0;
    int zte_normal = is_client_conn_normal(ZTE_CLIENT);
    int oth_normal = is_client_conn_normal(OTH_CLIENT);
    int is_normal = zte_normal | oth_normal;

    if (!is_normal)
    {
        s_need_get_slave_data = FALSE;
        return MQTT_NULL_ERR;
    }

    if (!s_need_get_slave_data)
    {
        return MQTT_NULL_ERR;
    }

    valid_dev = get_parallel_status()->valid_dev;
    if (valid_dev == 0)
    {
        return MQTT_NULL_ERR;
    }

    if (dev_addr > PV_NUM)
    {
        dev_addr = 1;
        s_need_get_slave_data = FALSE;
    }
    else if ((dev_addr == get_master_addr()) ||
        (((valid_dev >> (dev_addr - 1)) & 1) == 0) ||
        (handle_cur_slave_realdata_over(dev_addr, FALSE) == TRUE))
    {
        dev_addr++;
    }
    return MQTT_SUCCESS;
}

void handle_send_immediate_realdata_msg(_rt_msg_t curr_msg)
{
    char addr = *(char*)curr_msg->msg.data;
    rt_kprintf("mqtt rcv immediate report msg \n");
    handle_cur_slave_realdata_over(addr, TRUE);
}

int is_master_immediate_data_chg()
{
    unsigned short power_ctrl_para1[] = { DAC_PARA_ID_ACTIVE_POWER_DERATING_SETTING_OFFSET, DAC_PARA_ID_ACTIVE_POWER_DERATING_PERCENT_OFFSET,
    DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR_OFFSET, DAC_PARA_ID_REACTIVE_POWER_COMPEN_SETTING_OFFSET, DAC_PARA_ID_REACTIVE_POWER_VALUE_OFFSET, DAC_PARA_ID_REACTIVE_POWER_PCT_OFFSET};
    unsigned int power_ctrl_data1[] = {DAC_DATA_ID_ACTIVE_POWER_DERATING_SETTING_CURR, DAC_DATA_ID_ACTIVE_POWER_DERATING_PERCENT_CURR,
    DAC_DATA_ID_REACTIVE_POWER_COMPEN_SETTING_FACTOR_CURR, DAC_DATA_ID_REACTIVE_POWER_COMPEN_SETTING_CURR, DAC_DATA_ID_REACTIVE_POWER_VALUE_CURR, DAC_DATA_ID_REACTIVE_POWER_PCT_CURR};
    static float s_power_ctrl_value1[] = {0.0, 0.0, 0.0, 0.0, 0.0, 0.0};

    unsigned short power_ctrl_para2[] = {DAC_PARA_ID_ACTIVE_POWER_CONTROL_MODE_OFFSET, DAC_PARA_ID_REACTIVE_POWER_COMPEN_MODE_OFFSET};
    unsigned int power_ctrl_data2[] = {DAC_DATA_ID_ACTIVE_POWER_CONTROL_MODE_CURR, DAC_DATA_ID_REACTIVE_POWER_COMPEN_MODE_CURR};
    static unsigned short s_power_ctrl_value2[] = {0, 0};

    unsigned short open_off_status = 0;
    static unsigned short s_open_off_status = 0;

    unsigned short version_para[] = {DAC_PARA_ID_CSC_VERSION_OFFSET, DAC_PARA_ID_PSC_VERSION_OFFSET};
    unsigned int version_sid[] = {DAC_DATA_ID_CSC_VERSION_CURR, DAC_DATA_ID_PSC_VERSION_CURR};
    char version[32] = {};
    static char s_version[2][32] = {};

    unsigned int sta_signal_sid[] = {DAC_DATA_ID_ALARM_TOTAL_SIGNAL, DAC_DATA_ID_ACCI_TOTAL_SIGNAL, DAC_DATA_ID_AFCI_FILE_STATUS};
    static unsigned char s_signal[] = {0, 0, 0};

    int loop = 0;
    float f_value = 0.0;
    unsigned short us_value = 0;
    unsigned char uc_value = 0;
    int chg_cnt = 0;
    for(loop = 0; loop < sizeof(power_ctrl_para1) / sizeof(unsigned short); loop ++)
    {
        get_one_para(power_ctrl_para1[loop], &f_value);
        if(FLOAT_EQUAL(f_value, s_power_ctrl_value1[loop]))
        {
            continue;
        }
        rt_kprintf("power_ctrl_para1|sid:0x%x, value:%f, %f\n", power_ctrl_para1[loop], s_power_ctrl_value1[loop], f_value);
        set_one_data(power_ctrl_data1[loop], &f_value);
        s_power_ctrl_value1[loop] = f_value;
        chg_cnt ++;
    }

    for(loop = 0; loop < sizeof(power_ctrl_para2) / sizeof(unsigned short); loop ++)
    {
        get_one_para(power_ctrl_para2[loop], &us_value);
        if(us_value == s_power_ctrl_value2[loop])
        {
            continue;
        }
        rt_kprintf("power_ctrl_para2|sid:0x%x, value:%d\n", power_ctrl_para2[loop], s_power_ctrl_value2[loop], us_value);
        set_one_data(power_ctrl_data2[loop], &us_value);
        s_power_ctrl_value2[loop] = us_value;
        chg_cnt ++;
    }

    get_one_para(DAC_PARA_ID_POWER_ON_OFF_OFFSET, &open_off_status);
    if(s_open_off_status != open_off_status)
    {
        rt_kprintf("open_off_status|value:%d, %d\n", s_open_off_status, open_off_status);
        set_one_data(DAC_DATA_ID_POWER_ON_OFF_STA, &open_off_status);
        s_open_off_status = open_off_status;
        chg_cnt ++;
    }

    for(loop = 0; loop < sizeof(version_sid) / sizeof(unsigned int); loop ++)
    {
        rt_memset(version, 0 , 32);
        get_one_para(version_para[loop], version); 
        CONTINUE_IF_FAIL(rt_memcmp(version, s_version[loop], 32) != 0);
        rt_kprintf("version|sid:0x%x, version:%s, %s\n", version_sid[loop], s_version[loop], version);
        set_one_data(version_sid[loop], version);
        rt_memcpy(s_version[loop], version, 32);
        chg_cnt ++;
    }

    for(loop = 0; loop < sizeof(sta_signal_sid) / sizeof(unsigned int); loop ++)
    {
        get_one_data(sta_signal_sid[loop], &uc_value);
        if(uc_value == s_signal[loop])
        {
            continue;
        }
        rt_kprintf("sta_signal_sid|sid:0x%x, value:%d\n", sta_signal_sid[loop], uc_value);
        s_signal[loop] = uc_value;
        chg_cnt ++;
    }

    return chg_cnt;
}

int judge_master_immediate_to_network()
{
    char master_addr = 0;
    if(0 != is_master_immediate_data_chg())
    {
        master_addr = get_master_addr();
        send_msg_to_thread(MQTT_IMMEDIATE_SUBMIT_REAL, MOD_MQTT, &master_addr, sizeof(char));
    }
    return SUCCESSFUL;
}


