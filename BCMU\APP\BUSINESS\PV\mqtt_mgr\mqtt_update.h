#ifndef _MQTTT_UPDATE_H_
#define _MQTTT_UPDATE_H_

#ifdef __cplusplus
extern "C" {
#endif    //  __cplusplus

#include "mqttclient.h"
#include "softbus.h"
#include "update_download_manage.h"
#include "ee_public_info.h"

#define FILE_PATH_LEN                   200                 // 文件路径长度
#define DIFF_UPDATE_NAME_LEN            32
#define FILE_TYPE_COUNT                 3

#define CHECK_DELAY_UPDATE_STATUS_TIME  60000

#define CPLD_INDX   2
#define AUXI_INDX   1
#define MASTER_INDX 0

#define FILE_COUNT 3

#define TAR_VER_INFO_INDEX  12

typedef struct
{
    unsigned short file_type;
    unsigned short file_crc;
    char file_version[16];
}update_file_info_t;

typedef struct {
    char* name;
    char* file_name;
    int flag;
}crt_file_info_t;

int judge_power_file_version(unsigned short* addr_info, int idx);
int judge_moniter_file_version(unsigned short* addr_info, int idx);
int judge_download_file(unsigned short* addr_info,unsigned short real_addr_info);
int set_slave_version(unsigned int po_sid, char* version, int slave_addr);
int handle_update(void* client, message_data_t* msg);
int send_update_result(void);
void handle_send_update_msg(_rt_msg_t curr_msg);
int download_file(char* url, int file_type, unsigned int* file_size, unsigned int* file_crc, int which_tls);
int deal_update(int file_type, unsigned int file_size, unsigned int file_crc);
int network_update_deal(int opt, int file_type, unsigned int file_size, unsigned int file_crc, short addr_info);
void handle_deal_update_res(_rt_msg_t curr_msg);
int deal_file_download(update_app_info_t* update_info, unsigned short addr_info);
void handle_power_parallel_update(update_app_info_t* update_info);
int mqtt_start_update(unsigned short addr_info, unsigned int file_size, unsigned int file_crc);
int deal_update_rst(update_app_info_t* update_info, void* parallel_update_info, unsigned char addr);
int create_mqtt_send_update_timer();
void send_mqtt_update_msg(void *parameter);
int start_update_timer(unsigned int tm);
int judge_update_rst();
int get_moniter_update_rst();
int deal_version_info(update_app_info_t* update_info);
int deal_auxi_update(update_app_info_t* update_info);
int mqtt_download_file(char* url, char* file_name, char* file_path, unsigned int* file_size, unsigned int* file_crc, int which_tls);
int process_download_result(int download_rst, unsigned short addr_info, unsigned int file_size);
unsigned char deal_mqtt_update_rst(int loop);
int deal_delay_update_info(unsigned char delay_update, unsigned short addr_info);
int handle_mqtt_update();
void handle_mqtt_update_msg(_rt_msg_t curr_msg);
void handle_delay_update_status(_rt_msg_t curr_msg);
int judge_delay_update_status();
void delay_update_status_timeout(void *parameter);
int deal_update_dev_info(unsigned char delay_update, unsigned short addr_info);
int deal_ack_updating(char* uuid, unsigned short addr_info);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif // _MQTTT_UPDATE_H_
