#include <string.h>
#include <stdlib.h>
#include <rtthread.h>
#include "dev_acmu.h"
#include "his_record.h"
#include "MIBTable.h"
#include "sps.h"
#include "protocol_layer.h"
#include "realdata_id_in.h"
#include "alarm_id_in.h"
#include "para_id_in.h"
#include "para_manage.h"
#include "realdata_save.h"
#include "acmu_protocol_north.h"
#include "dev_acmu.h"
#include "alarm_manage.h"
#include "const_define_in.h"
#include "drv_utils.h"
#include "pdt_version.h"
#include "utils_time.h"
#include "utils_data_transmission.h"
#include "utils_rtthread_security_func.h"
#include "utils_data_type_conversion.h"

Static rt_timer_t s_reset_delay = NULL;

Static int pack_real_data(void* dev_inst, void *cmd_buf);
Static int ctrl_cmd(void* dev_inst, void *cmd_buf);
Static char soft_sysreset_handle(void);
Static unsigned short get_para_crc (void);
Static int parse_real_alarm(void* dev_inst, void *cmd_buf);
Static int parse_his_alarm(void* dev_inst, void *cmd_buf);
Static int pack_real_alarm(void* dev_inst, void *cmd_buf);
Static int pack_his_alarm(void* dev_inst, void *cmd_buf);
Static int pack_comm_func(void* dev_inst, void *cmd_buf);
Static int parse_set_public_para(void* dev_inst, void *cmd_buf);
Static int pack_public_para(void* dev_inst, void *cmd_buf);
Static int if_filter_alarm(unsigned char uc_ID2);
Static int HisAlmToBuff(unsigned short index, cmd_buf_t *cmd_buf);
Static int pack_factory_info(void* dev_inst, void *cmd_buf);
Static int parse_set_time(void* dev_inst, void *cmd_buf);

Static unsigned char s_command_type = 0x00;

static unsigned int s_alarm_id[] RAM_SECTION = {
    GET_ALM_ID(ACMU_ALM_ID_AC_POWER_OFF , 1 , 1 ),             //交流停电
    GET_ALM_ID(ACMU_ALM_ID_PHASE_VOLT_HIGH_ALARM , 1 , 1 ),             //交流A相电压高
    GET_ALM_ID(ACMU_ALM_ID_PHASE_VOLT_HIGH_ALARM , 2 , 1 ),             //交流B相电压高
    GET_ALM_ID(ACMU_ALM_ID_PHASE_VOLT_HIGH_ALARM , 3 , 1 ),             //交流C相电压高
    GET_ALM_ID(ACMU_ALM_ID_PHASE_VOLT_LOW_ALARM , 1 , 1 ),              //交流A相电压低
    GET_ALM_ID(ACMU_ALM_ID_PHASE_VOLT_LOW_ALARM , 2 , 1 ),              //交流B相电压低
    GET_ALM_ID(ACMU_ALM_ID_PHASE_VOLT_LOW_ALARM , 3 , 1 ),              //交流C相电压低
    GET_ALM_ID(ACMU_ALM_ID_AC_PHASE_LOST , 1 , 1 ),            //交流缺相L1
    GET_ALM_ID(ACMU_ALM_ID_AC_PHASE_LOST , 2 , 1 ),            //交流缺相L2
    GET_ALM_ID(ACMU_ALM_ID_AC_PHASE_LOST , 3 , 1 ),            //交流缺相L3
    GET_ALM_ID(ACMU_ALM_ID_PHASE_VOLT_UNBALANCE , 1 , 1 ),     //相电压不平衡
    GET_ALM_ID(ACMU_ALM_ID_PHASE_CURR_HIGH_ALARM , 1 , 1 ),             //交流相电流高
    GET_ALM_ID(ACMU_ALM_ID_PHASE_CURR_HIGH_ALARM , 2 , 1 ),             //交流相电流高
    GET_ALM_ID(ACMU_ALM_ID_PHASE_CURR_HIGH_ALARM , 3 , 1 ),             //交流相电流高
    GET_ALM_ID(ACMU_ALM_ID_SPD_C , 1 , 1 ),                    //C级防雷器损坏
    GET_ALM_ID(ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION , 1 , 1 ), //交流输出空开断
    GET_ALM_ID(ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION , 2 , 1 ), //交流输出空开断
    GET_ALM_ID(ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION , 3 , 1 ), //交流输出空开断
    GET_ALM_ID(ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION , 4 , 1 ), //交流输出空开断
    GET_ALM_ID(ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION , 5 , 1 ), //交流输出空开断
    GET_ALM_ID(ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION , 6 , 1 ), //交流输出空开断
    GET_ALM_ID(ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION , 7 , 1 ), //交流输出空开断
    GET_ALM_ID(ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION , 8 , 1 ), //交流输出空开断
    GET_ALM_ID(ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION , 9 , 1 ), //交流输出空开断
    GET_ALM_ID(ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION , 10 , 1 ), //交流输出空开断
    GET_ALM_ID(ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION , 11 , 1 ), //交流输出空开断
    GET_ALM_ID(ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION , 12 , 1 ), //交流输出空开断
    GET_ALM_ID(ACMU_ALM_ID_AC_LINE_VOLT_HIGH , 1 , 1 ),          //交流线L1/L2电压高
    GET_ALM_ID(ACMU_ALM_ID_AC_LINE_VOLT_HIGH , 2 , 1 ),          //交流线L2/L3电压高
    GET_ALM_ID(ACMU_ALM_ID_AC_LINE_VOLT_HIGH , 3 , 1 ),          //交流线L3/L1电压高
    GET_ALM_ID(ACMU_ALM_ID_AC_LINE_VOLT_LOW , 1 , 1 ),           //交流线L1/L2电压低
    GET_ALM_ID(ACMU_ALM_ID_AC_LINE_VOLT_LOW , 2 , 1 ),           //交流线L2/L3电压低
    GET_ALM_ID(ACMU_ALM_ID_AC_LINE_VOLT_LOW , 3 , 1 ),           //交流线L3/L1电压低
};

static unsigned char cmd_req[] RAM_SECTION = 
{
    ORDER_GET,                  //0
    ORDER_CTRL,                 //1
    ORDER_GETREALALM,           //2
    ORDER_GETPARA,              //3
    ORDER_SETPARA,              //4
    ORDER_GETHISALM,            //5
    ORDER_GETFACTINFO,          //6
    ORDER_SETTIME,              //7
};
static unsigned char cmd_ack[] RAM_SECTION = 
{
    ORDER_GET,                 //0
    ORDER_CTRL,                //1
    ORDER_GETREALALM,          //2
    ORDER_GETPARA,             //3
    ORDER_SETPARA,             //4
    ORDER_GETHISALM,           //5
    ORDER_GETFACTINFO,         //6
    ORDER_SETTIME,             //7
};


static cmd_t no_poll_cmd_tab[] = 
{
    {ACMU_NORTH_GET_DATA, CMD_PASSIVE, &cmd_req[0], &cmd_ack[0], sizeof(unsigned char),   NULL, NULL, pack_real_data, NULL,},
    {ACMU_NORTH_CTRL_CMD, CMD_PASSIVE, &cmd_req[1], &cmd_ack[1], sizeof(unsigned char),   NULL, NULL, pack_comm_func, ctrl_cmd,},
    {ACMU_NORTH_GET_ALARM, CMD_PASSIVE, &cmd_req[2], &cmd_ack[2], sizeof(unsigned char),   NULL, NULL, pack_real_alarm, parse_real_alarm,},
    {ACMU_NORTH_GET_PARA, CMD_PASSIVE, &cmd_req[3], &cmd_ack[3], sizeof(unsigned char),   NULL, NULL, pack_public_para, NULL,},
    {ACMU_NORTH_SET_PARA, CMD_PASSIVE, &cmd_req[4], &cmd_ack[4], sizeof(unsigned char),   NULL, NULL, pack_comm_func, parse_set_public_para,},
    {ACMU_NORTH_GET_HIS_ALM, CMD_PASSIVE, &cmd_req[5], &cmd_ack[5], sizeof(unsigned char),   NULL, NULL, pack_his_alarm, parse_his_alarm,},
    {ACMU_NORTH_GET_FAC, CMD_PASSIVE, &cmd_req[6], &cmd_ack[6], sizeof(unsigned char),   NULL, NULL, pack_factory_info, NULL,},
    {ACMU_NORTH_SET_TIME, CMD_BROADCAST, &cmd_req[7], &cmd_ack[7], sizeof(unsigned char),   NULL, NULL, NULL, parse_set_time,},
    {0,},
};

Static dev_type_t dev_acmu_north = 
{
    DEV_CSU, 1, PROTOCOL_NORTH_COMM, LINK_ACMU_NORTH, R_BUFF_LEN, S_BUFF_LEN, 0, &no_poll_cmd_tab[0],
};

dev_type_t* init_dev_acmu_north(void){
    return &dev_acmu_north;
}


Static int pack_factory_info(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned int offset = 0;
    char sm_name[LEN_SMNAME] = {0};
    char reserved[LEN_RESERVED] = {0};

    unsigned char data_flag1 = 0;
    unsigned char data_flag2 = 0;
    unsigned short debug_cmd_cnt = 0;

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);

    get_one_data(ACMU_DATA_ID_GET_FACT_INFO_CNT, &debug_cmd_cnt);
    debug_cmd_cnt++;
    set_one_data(ACMU_DATA_ID_GET_FACT_INFO_CNT, &debug_cmd_cnt);
    
    buff = cmd_buf_temp->buf;

    offset++;         //长度占位

    get_one_data(ACMU_DATA_ID_DATA_FLAG1, &data_flag1);
    buff[offset++] = data_flag1 | FLAG1_E | FLAG1_F;         // data_flag1;
    
    get_one_data(ACMU_DATA_ID_DATA_FLAG2, &data_flag2);
    data_flag2 &= ~FLAG2_RST;
    set_one_data(ACMU_DATA_ID_DATA_FLAG2, &data_flag2);
    buff[offset++] = data_flag2;                             // data_flag2;

    rt_memset_s(sm_name, LEN_SMNAME, 0x20, LEN_SMNAME);
    rt_memcpy_s(sm_name, LEN_SMNAME, SOFTWARE_NAME, rt_strnlen_s(SOFTWARE_NAME, LEN_SMNAME));
    rt_memcpy_s(&buff[offset], LEN_SMNAME, &sm_name[0], LEN_SMNAME);
    offset += LEN_SMNAME;

    buff[offset++] = SOFTWARE_VER1;                           // 软件主版本
    buff[offset++] = SOFTWARE_VER2;                           // 软件副版本
    buff[offset++] = SOFTWARE_VER3;                           // 软件子版本

    buff[offset++] = (unsigned char)(SOFTWARE_DATE_YEAR >> 8);   // 年份高字节
    buff[offset++] = (unsigned char)(SOFTWARE_DATE_YEAR & 0xFF); // 年份低字节
    buff[offset++] = SOFTWARE_DATE_MONTH;                        // 月
    buff[offset++] = SOFTWARE_DATE_DAY;                          // 日

    rt_memcpy_s(&buff[offset], LEN_RESERVED, &reserved, LEN_RESERVED);             // 预留
    offset += LEN_RESERVED;

    buff[0] = offset - 3;                     //长度
    ((cmd_buf_t*)cmd_buf)->data_len = offset;
    return SUCCESSFUL;
}

Static int parse_set_time(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned int offset = 0;
    time_base_t tm = {0};
    unsigned char data_flag1 = 0;
    unsigned short debug_cmd_cnt = 0;
    
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);

    get_one_data(ACMU_DATA_ID_SET_TIME_CNT, &debug_cmd_cnt);
    debug_cmd_cnt++;
    set_one_data(ACMU_DATA_ID_SET_TIME_CNT, &debug_cmd_cnt);

    buff = cmd_buf_temp->buf;

    offset++;
    tm.year = get_int16_data(&buff[offset]);
    offset+=2;
    tm.month = buff[offset++];
    tm.day = buff[offset++];
    tm.hour = buff[offset++];
    tm.minute = buff[offset++];
    tm.second = buff[offset++];
    if( check_time_range(tm) == FAILURE )
    {
        cmd_buf_temp->rtn = RTNRS485_INVLDATA;
        return SUCCESSFUL;
    }

    get_one_data(ACMU_DATA_ID_DATA_FLAG1, &data_flag1);
    data_flag1 &= ~FLAG1_TIMECH;
    set_one_data(ACMU_DATA_ID_DATA_FLAG1, &data_flag1);

    /* 设置系统时间 */
    if (set_system_time(&tm) != SUCCESSFUL) {
        cmd_buf_temp->rtn = RTNRS485_INVLDATA;
        return SUCCESSFUL;
    }

    return SUCCESSFUL;
}




Static unsigned short get_para_crc(void) {
    unsigned short para_crc = 0xFFFF;
    unsigned char prtcl_mode = UNKNOW_MODE;
    acmu_para_data_t acmu_para_data = {0};

    // 获取协议模式
    get_one_data(ACMU_DATA_ID_PRTCL_MODE, &prtcl_mode);

    get_one_para(ACMU_PARA_ID_PHASE_VOLTAGE_UNBALANCE_OFFSET, &acmu_para_data.phase_voltage_unbalance_offset);
    get_one_para(ACMU_PARA_ID_AC_PHASE_VOLTAGE_MAX_OFFSET, &acmu_para_data.ac_phase_voltage_max_offset);
    get_one_para(ACMU_PARA_ID_AC_PHASE_VOLTAGE_MIN_OFFSET, &acmu_para_data.ac_phase_voltage_min_offset);
    get_one_para(ACMU_PARA_ID_AC_IN_CURRENT_MAX_OFFSET, &acmu_para_data.ac_current_max_offset);
    get_one_para(ACMU_ALM_ID_AC_POWER_OFF_LEVEL, &acmu_para_data.power_off_alarm_level);
    get_one_para(ACMU_ALM_ID_AC_PHASE_LOST_LEVEL, &acmu_para_data.phase_lost_alarm_level);
    get_one_para(ACMU_ALM_ID_PHASE_VOLT_UNBALANCE_LEVEL, &acmu_para_data.phase_volt_unbalance_alarm_level);
    get_one_para(ACMU_ALM_ID_PHASE_VOLT_HIGH_ALARM_LEVEL, &acmu_para_data.volt_high_alarm_level);
    get_one_para(ACMU_ALM_ID_PHASE_VOLT_LOW_ALARM_LEVEL, &acmu_para_data.volt_low_alarm_level);
    get_one_para(ACMU_ALM_ID_PHASE_CURR_HIGH_ALARM_LEVEL, &acmu_para_data.curr_hign_alarm_level);
    get_one_para(ACMU_ALM_ID_SPD_C_LEVEL, &acmu_para_data.spd_c_alarm_level);
    get_one_para(ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION_LEVEL, &acmu_para_data.output_switch_disconnection_alarm_level);

    // 根据协议模式设置para_crc
    switch (prtcl_mode) {
        case UNKNOW_MODE:
            break;
        case OLD_MODE:
            para_crc = crc_cal((unsigned char *)&acmu_para_data, (offsetof(acmu_para_data_t, output_switch_disconnection_alarm_level) + sizeof(acmu_para_data.output_switch_disconnection_alarm_level)));
            set_one_data(ACMU_DATA_ID_OLD_PARA_CRC, &para_crc);
            break;
        case NEW_MODE:
            get_one_para(ACMU_PARA_ID_AC_LINE_VOLTAGE_MAX_OFFSET, &acmu_para_data.ac_line_voltage_max);
            get_one_para(ACMU_PARA_ID_AC_LINE_VOLTAGE_MIN_OFFSET, &acmu_para_data.ac_line_voltage_min);
            get_one_para(ACMU_ALM_ID_AC_LINE_VOLT_HIGH_LEVEL, &acmu_para_data.ac_line_voltage_max_alarm_level);
            get_one_para(ACMU_ALM_ID_AC_LINE_VOLT_LOW_LEVEL, &acmu_para_data.ac_line_voltage_min_alarm_level);

            para_crc = crc_cal((unsigned char *)&acmu_para_data, (offsetof(acmu_para_data_t, ac_line_voltage_min_alarm_level) + sizeof(acmu_para_data.ac_line_voltage_min_alarm_level)));
            set_one_data(ACMU_DATA_ID_PARA_CRC, &para_crc);
            break;
        default:
            // 处理未知模式，可以设置为默认值或错误处理
            para_crc = 0xFFFF;
            break;
    }

    return para_crc;
}

Static int pack_real_data(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned int offset = 0;
    float f_data = 0.0;
    unsigned char uc_data = 0;
    unsigned short para_crc = 0;
    acem_analog_data_t acem_show_data;
    int i = 0;
    unsigned char data_flag1 = 0;
    unsigned char data_flag2 = 0;
    unsigned short debug_cmd_cnt = 0;

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);

    get_one_data(ACMU_DATA_ID_REC_CALL_CMD_CNT, &debug_cmd_cnt);
    debug_cmd_cnt++;
    set_one_data(ACMU_DATA_ID_REC_CALL_CMD_CNT, &debug_cmd_cnt);

    buff = cmd_buf_temp->buf;

    rt_memset_s(&acem_show_data, sizeof(acem_show_data), 0, sizeof(acem_show_data));
    get_acem_show_data(&acem_show_data);

    offset++;         //长度占位

    get_one_data(ACMU_DATA_ID_DATA_FLAG1, &data_flag1);
    data_flag1 = data_flag1 | FLAG1_E | FLAG1_F;
    buff[offset++] = data_flag1;                             // data_flag1;

    get_one_data(ACMU_DATA_ID_DATA_FLAG2, &data_flag2);
    buff[offset++] = data_flag2;                             // data_flag2;

    for(i = 0; i < AC_PHASE_NUM; i++) {
        get_one_data(ACMU_DATA_ID_PHASE_VOLT + i, &f_data);
        put_int16_to_buff(&buff[offset], round(f_data));      //相电压
        offset += 2;
    }

    for(i = 0; i < AC_PHASE_NUM; i++) {
        get_one_data(ACMU_DATA_ID_PHASE_CURR + i, &f_data);
        put_int16_to_buff(&buff[offset], round(f_data));      //相电流
        offset += 2;
    }

    buff[offset++]    = 46;                             // 预留模拟量字节数P ＝46

    para_crc = get_para_crc();
    put_uint16_to_buff(&buff[offset], para_crc);                         //参数crc
    offset += 2;

    for(i = 0; i < AC_PHASE_NUM; i++) {
        put_int16_to_buff(&buff[offset], round(POW_PREC_AC_VOLT*acem_show_data.phase_volt[i]));      //交流电表相电压
        offset += 2;
    }

    for(i = 0; i < AC_PHASE_NUM; i++) {
        put_int16_to_buff(&buff[offset], round(POW_PREC_AC_CURR*acem_show_data.phase_curr[i]));      //交流电表电流
        offset += 2;
    }

    for(i = 0; i < AC_PHASE_NUM; i++) {
        put_int16_to_buff(&buff[offset], round(POW_PREC_ACEM_POWER_FACT*acem_show_data.power_factor[i]));      //交流电表相功率因数
        offset += 2;
    }

    put_int32_to_buff(&buff[offset], round(POW_PREC_ACEM_POWER*acem_show_data.total_act_power));    //有功功率
    offset += 4;

    put_int32_to_buff(&buff[offset], round(POW_PREC_ACEM_POWER*acem_show_data.total_react_power));    //无功功率
    offset += 4;

    put_int32_to_buff(&buff[offset], round(POW_PREC_ACEM_POWER*acem_show_data.total_apparent_power));    //视在功率
    offset += 4;

    put_uint32_to_buff(&buff[offset], round(POW_PREC_ACEM_TOTAL_ENERGY*acem_show_data.total_energy));    //总电能
    offset += 4;

    put_int16_to_buff(&buff[offset], round(POW_PREC_ACEM_POWER_FACT*acem_show_data.total_power_factor));    //总功率因子
    offset += 2;

    put_int16_to_buff(&buff[offset], acem_show_data.frequency);    //频率
    offset += 2;

    for(i = 0; i < AC_PHASE_NUM; i++) {
        get_one_data(ACMU_DATA_ID_LINE_VOLT + i, &f_data);
        put_int16_to_buff(&buff[offset], f_data);                         //交流线电压
        offset += 2;
    }

    buff[offset++] = 3; // 预留配置参数字节数 S = 3

    for(i = 0; i < AC_PHASE_NUM; i++) {
        get_one_para(ACMU_PARA_ID_SYS_AC_MUTUAL_INDUCTANCE_CONFIGURATION_OFFSET + i, &uc_data);
        buff[offset++] = uc_data;
    }

    get_one_data(ACMU_DATA_ID_AC_INPUT_SWITCH, &uc_data);
    buff[offset++] = uc_data;

    get_one_data(ACMU_DATA_ID_POWER_SOURCE, &uc_data);
    buff[offset++] = uc_data;

    buff[offset++]    = RELAY_NUM;                    //输入干接点状态
    for( i = 0; i < RELAY_NUM; i++ )
    {
        get_one_data(ACMU_DATA_ID_INPUT_RELAY + i, &uc_data);
        buff[offset++]    = uc_data;                    // ACMU输入干结点
    }

    buff[offset++]    = 0;                    //预留参数量字节数 N = 0
     
    buff[0] = offset - 3;                     //长度填充
    ((cmd_buf_t*)cmd_buf)->data_len = offset;

    return SUCCESSFUL;
}

Static char soft_sysreset_handle(void)
{
    if (s_reset_delay == NULL)
    {
        s_reset_delay = rt_timer_create("softreset", NVIC_SystemReset, NULL, 1000, RT_TIMER_FLAG_ONE_SHOT);
    }

    if (s_reset_delay != NULL)
    {
        rt_timer_stop(s_reset_delay);
        rt_timer_start(s_reset_delay);
    }

    return SUCCESSFUL;
}

Static int ctrl_cmd(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char cid1,cid2 = 0;
    unsigned char* buff;
    unsigned short debug_cmd_cnt = 0;
    
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);

    buff = cmd_buf_temp->buf;

    RETURN_VAL_IF_FAIL(buff != NULL, FAILURE);

    get_one_data(ACMU_DATA_ID_REC_CTRL_CMD_CNT, &debug_cmd_cnt);
    debug_cmd_cnt++;
    set_one_data(ACMU_DATA_ID_REC_CTRL_CMD_CNT, &debug_cmd_cnt);

    cid1 = buff[0];
    cid2 = buff[1];

    if(cid1 == 0x14 && cid2 == 0xf3) //遥控复位
    {
        soft_sysreset_handle();
    }
    else
    {
        cmd_buf_temp->rtn = RTN_WRONG_COMMAND;
        cmd_buf_temp->data_len = 0;
    }

    return SUCCESSFUL;
}

Static int parse_real_alarm(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);

    buff = cmd_buf_temp->buf;

    RETURN_VAL_IF_FAIL(buff != NULL, FAILURE);

    if(cmd_buf_temp->data_len > 0) {
        s_command_type = buff[0];
    } else {
        s_command_type = WRONG_DATA_LENGTH;
    }
    return SUCCESSFUL;
}


Static int if_filter_alarm(unsigned char uc_ID2) {
    unsigned char prtcl_mode = 0;

    // 获取协议模式
    get_one_data(ACMU_DATA_ID_PRTCL_MODE, &prtcl_mode);

    // 检查协议模式和告警ID
    if ((prtcl_mode == UNKNOW_MODE || prtcl_mode == OLD_MODE) &&
        (uc_ID2 == ID2_ALM_ACVOLTPPHIGH || uc_ID2 == ID2_ALM_ACVOLTPPLOW)) {
        return TRUE;
    }

    return FALSE;
}



Static int pack_real_alarm(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned int offset = 0;
    unsigned char data_flag1 = 0;
    unsigned char data_flag2 = 0;
    static unsigned short s_send_index = 0;
    static unsigned short s_send_index_bak = 0;
    real_alarm_t* real_alm = NULL;
    alarm_map_t* alarm_map = NULL;
    unsigned short debug_cmd_cnt = 0;
    
    unsigned char uc_ID2 = 0;
    unsigned char uc_index = 0;
    int ret = 0;

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);

    get_one_data(ACMU_DATA_ID_GET_REAL_ALM_CNT, &debug_cmd_cnt);
    debug_cmd_cnt++;
    set_one_data(ACMU_DATA_ID_GET_REAL_ALM_CNT, &debug_cmd_cnt);

    buff = cmd_buf_temp->buf;  

    offset++;         // 长度占位 

    get_one_data(ACMU_DATA_ID_DATA_FLAG1, &data_flag1);
    buff[offset++] = data_flag1;      // data_flag1;
    get_one_data(ACMU_DATA_ID_DATA_FLAG2, &data_flag2);
    buff[offset++] = data_flag2;      // data_flag2;

    switch (s_command_type) {
        case GROUP_TYPE_RCV_START:
            s_send_index = 0;
            data_flag1 &= ~FLAG1_ALMCH;   // 清除告警变化标志位
            set_one_data(ACMU_DATA_ID_DATA_FLAG1, &data_flag1);
            break;
        case GROUP_TYPE_RCV_NEXT:
            break;
        case GROUP_TYPE_RCV_ERROR:
            s_send_index = s_send_index_bak;
            break;
        default:
            buff[offset++] = RTN_INVLDATA;
            buff[OFFSET_DATALEN] = offset - 3;                     // 长度填充
            buff[OFFSET_FLAG1] = data_flag1 | FLAG1_E | FLAG1_F;
            cmd_buf_temp->data_len = offset;
            cmd_buf_temp->rtn = RTN_INVLDATA;
            
            return SUCCESSFUL;
    }

    s_send_index_bak = s_send_index;  // 备份告警id数组的遍历首索引

    if (s_send_index == 0) {          // 如果是发送第一包数据则将commandtype置为0
        buff[offset++] = GROUP_TYPE_SEND_START;       // 把commandtype置为0
    } else {
        buff[offset++] = GROUP_TYPE_SEND_NEXT;        // 把commandtype置为1
    }

    for (; s_send_index < sizeof(s_alarm_id) / sizeof(s_alarm_id[0]); s_send_index++) {
        if (offset > ACMU_REAL_ALARM_DATALEN_MAX) {   // 如果数据包太长,分成多个包
            break;
        }

        real_alm = get_realtime_alarm(s_alarm_id[s_send_index]);

        if (real_alm == NULL) {
            continue;
        }

        ret = get_alarm_sn(ALM_ID_GET_ALM_CODE(s_alarm_id[s_send_index]), &alarm_map);
        // 检查get_alarm_sn是否成功
        if (ret == -1) {
            continue;
        }

        uc_ID2 = alarm_map->id2;
        uc_index = ALM_ID_GET_DEV(s_alarm_id[s_send_index]);

        if (if_filter_alarm(uc_ID2)) {
            continue;
        }

        buff[offset++] = ID1_ACMU_ALARM;     // 告警ID1
        buff[offset++] = uc_ID2;      // 告警ID2
        if (uc_index > 0) {
            buff[offset++] = uc_index - 1;    // 告警Index
        } else {
            buff[offset++] = uc_index;        // 告警Index
        }
        buff[offset++] = get_real_alm_level(real_alm);  // 告警值  
        put_time_t_to_buff(&buff[offset], real_alm->start_time);  // 告警产生时间
        offset += 7;
    }

    if (s_send_index == sizeof(s_alarm_id) / sizeof(s_alarm_id[0])) {
        buff[OFFSET_COMMANDTYPE] = GROUP_TYPE_SEND_END;  // 最后一包数据,CommandType=0x02
        s_send_index = 0;
    }

    if (offset == 4) {  // 没有实时告警记录，只有CommandType和长度，data_flag,置位RTN无数据并把后台的ID返回
        buff[OFFSET_ERRID] = RTN_NODATA;
        cmd_buf_temp->rtn = RTN_NODATA;
    }

    buff[OFFSET_DATALEN] = offset - 3;                     // 长度填充
    buff[OFFSET_FLAG1] = data_flag1 | FLAG1_E | FLAG1_F;
    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}



Static int set_acmu_para_data(acmu_para_data_t* acmu_para_data, unsigned char acmu_para_type) {
    RETURN_VAL_IF_FAIL(acmu_para_data != NULL, RTN_INVLDATA);
    char rst = 0;

    rst |= set_one_para(ACMU_PARA_ID_PHASE_VOLTAGE_UNBALANCE_OFFSET, &acmu_para_data->phase_voltage_unbalance_offset, TRUE, FALSE);
    rst |= set_one_para(ACMU_PARA_ID_AC_PHASE_VOLTAGE_MAX_OFFSET, &acmu_para_data->ac_phase_voltage_max_offset, TRUE, FALSE);
    rst |= set_one_para(ACMU_PARA_ID_AC_PHASE_VOLTAGE_MIN_OFFSET, &acmu_para_data->ac_phase_voltage_min_offset, TRUE, FALSE);
    rst |= set_one_para(ACMU_PARA_ID_AC_IN_CURRENT_MAX_OFFSET, &acmu_para_data->ac_current_max_offset, TRUE, FALSE);
    rst |= set_alm_para(ACMU_ALM_ID_AC_POWER_OFF_LEVEL, &acmu_para_data->power_off_alarm_level, FALSE);
    rst |= set_alm_para(ACMU_ALM_ID_AC_PHASE_LOST_LEVEL, &acmu_para_data->phase_lost_alarm_level, FALSE);
    rst |= set_alm_para(ACMU_ALM_ID_PHASE_VOLT_UNBALANCE_LEVEL, &acmu_para_data->phase_volt_unbalance_alarm_level,  FALSE);
    rst |= set_alm_para(ACMU_ALM_ID_PHASE_VOLT_HIGH_ALARM_LEVEL, &acmu_para_data->volt_high_alarm_level, FALSE);
    rst |= set_alm_para(ACMU_ALM_ID_PHASE_VOLT_LOW_ALARM_LEVEL, &acmu_para_data->volt_low_alarm_level,  FALSE);
    rst |= set_alm_para(ACMU_ALM_ID_PHASE_CURR_HIGH_ALARM_LEVEL, &acmu_para_data->curr_hign_alarm_level,  FALSE);
    rst |= set_alm_para(ACMU_ALM_ID_SPD_C_LEVEL, &acmu_para_data->spd_c_alarm_level, FALSE);
    rst |= set_alm_para(ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION_LEVEL, &acmu_para_data->output_switch_disconnection_alarm_level,  FALSE);

    unsigned short wCRC = crc_cal((unsigned char *)acmu_para_data, (offsetof(acmu_para_data_t, output_switch_disconnection_alarm_level) + sizeof(acmu_para_data->output_switch_disconnection_alarm_level)));
    set_one_data(ACMU_DATA_ID_OLD_PARA_CRC, &wCRC);
    set_one_data(ACMU_DATA_ID_PRTCL_MODE, &acmu_para_type);

    if(acmu_para_type == NEW_MODE)
    {
        rst |= set_one_para(ACMU_PARA_ID_AC_LINE_VOLTAGE_MAX_OFFSET, &acmu_para_data->ac_line_voltage_max, TRUE, FALSE);
        rst |= set_one_para(ACMU_PARA_ID_AC_LINE_VOLTAGE_MIN_OFFSET, &acmu_para_data->ac_line_voltage_min, TRUE, FALSE);
        rst |= set_alm_para(ACMU_ALM_ID_AC_LINE_VOLT_HIGH_LEVEL, &acmu_para_data->ac_line_voltage_max_alarm_level, FALSE);
        rst |= set_alm_para(ACMU_ALM_ID_AC_LINE_VOLT_LOW_LEVEL, &acmu_para_data->ac_line_voltage_min_alarm_level, FALSE);

        unsigned short w_new_CRC = crc_cal((unsigned char *)acmu_para_data, (offsetof(acmu_para_data_t, ac_line_voltage_min_alarm_level) + sizeof(acmu_para_data->ac_line_voltage_min_alarm_level)));
        set_one_data(ACMU_DATA_ID_PARA_CRC, &w_new_CRC);
    }
    if(rst < 0)
    {
        return RTN_INVLDATA;
    }
    update_para();
    return SUCCESSFUL;
}



Static int pack_public_para(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned int offset = 0;
    unsigned char data_flag1 = 0;
    unsigned char data_flag2 = 0;
    unsigned char alarm_num_flag = 0;
    acmu_para_data_t acmu_para_data_s;
    unsigned short debug_cmd_cnt = 0;

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);

    buff = cmd_buf_temp->buf;

    RETURN_VAL_IF_FAIL(buff != NULL, FAILURE);

    get_one_data(ACMU_DATA_ID_GET_COMM_PARA_CNT, &debug_cmd_cnt);
    debug_cmd_cnt++;
    set_one_data(ACMU_DATA_ID_GET_COMM_PARA_CNT, &debug_cmd_cnt);

    offset++;         //长度占位

    get_one_data(ACMU_DATA_ID_DATA_FLAG1, &data_flag1);
    data_flag1 = data_flag1 | FLAG1_E | FLAG1_F;
    buff[offset++] = data_flag1;                             // data_flag1;
    
    get_one_data(ACMU_DATA_ID_DATA_FLAG2, &data_flag2);
    buff[offset++] = data_flag2;                             // data_flag2;

    get_one_para(ACMU_PARA_ID_PHASE_VOLTAGE_UNBALANCE_OFFSET, &acmu_para_data_s.phase_voltage_unbalance_offset);
    put_int16_to_buff(&buff[offset], acmu_para_data_s.phase_voltage_unbalance_offset);                         //相电压不平衡阀值
    offset += 2;

    get_one_para(ACMU_PARA_ID_AC_PHASE_VOLTAGE_MAX_OFFSET, &acmu_para_data_s.ac_phase_voltage_max_offset);
    put_int16_to_buff(&buff[offset], acmu_para_data_s.ac_phase_voltage_max_offset);                         //交流输入线/相电压上限
    offset += 2;

    get_one_para(ACMU_PARA_ID_AC_PHASE_VOLTAGE_MIN_OFFSET, &acmu_para_data_s.ac_phase_voltage_min_offset);
    put_int16_to_buff(&buff[offset], acmu_para_data_s.ac_phase_voltage_min_offset);                         //交流输入线/相电压下限
    offset += 2;

    get_one_para(ACMU_PARA_ID_AC_IN_CURRENT_MAX_OFFSET, &acmu_para_data_s.ac_current_max_offset);
    put_int16_to_buff(&buff[offset], acmu_para_data_s.ac_current_max_offset);                         //交流输入电流上限
    offset += 2;
    alarm_num_flag = offset++;

    get_alm_para(ACMU_ALM_ID_AC_POWER_OFF_LEVEL, &buff[offset++]);       //交流停电告警属性设置
    get_alm_para(ACMU_ALM_ID_AC_PHASE_LOST_LEVEL, &buff[offset++]);       //交流缺相告警属性设置
    get_alm_para(ACMU_ALM_ID_PHASE_VOLT_UNBALANCE_LEVEL, &buff[offset++]);       //交流相电压不平衡告警属性设置
    get_alm_para(ACMU_ALM_ID_PHASE_VOLT_HIGH_ALARM_LEVEL, &buff[offset++]);       //交流相电压高告警属性设置
    get_alm_para(ACMU_ALM_ID_PHASE_VOLT_LOW_ALARM_LEVEL, &buff[offset++]);       //交流相电压低告警属性设置
    get_alm_para(ACMU_ALM_ID_PHASE_CURR_HIGH_ALARM_LEVEL, &buff[offset++]);       //交流相电流高告警属性设置
    get_alm_para(ACMU_ALM_ID_SPD_C_LEVEL, &buff[offset++]);       //C级防雷器损坏告警属性设置
    get_alm_para(ACMU_ALM_ID_AC_OUTPUT_SWITCH_DISCONNECTION_LEVEL, &buff[offset++]);       //交流输出空开断告警属性设置

    buff[alarm_num_flag] = (offset - 1) - alarm_num_flag;//告警属性设置个数
    
    buff[0] = offset - 3;                     // 长度填充
    cmd_buf_temp->data_len = offset;

    return SUCCESSFUL;
}



Static int parse_set_public_para(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned int offset = 0;
    acmu_para_data_t acmu_para_data;
    unsigned short debug_cmd_cnt = 0;
    
    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);

    buff = cmd_buf_temp->buf;

    RETURN_VAL_IF_FAIL(buff != NULL, FAILURE);

    get_one_data(ACMU_DATA_ID_SET_COMM_PARA_CNT, &debug_cmd_cnt);
    debug_cmd_cnt++;
    set_one_data(ACMU_DATA_ID_SET_COMM_PARA_CNT, &debug_cmd_cnt);
    
    // 相电压不平衡阈值
    acmu_para_data.phase_voltage_unbalance_offset = get_int16_data(&buff[offset]);
    offset += 2;
    
    // 交流相电压高阈值
    acmu_para_data.ac_phase_voltage_max_offset = get_int16_data(&buff[offset]);
    offset += 2;

    // 交流相电压低阈值
    acmu_para_data.ac_phase_voltage_min_offset = get_int16_data(&buff[offset]);
    offset += 2;
    
    // 交流电流高阈值
    acmu_para_data.ac_current_max_offset = get_int16_data(&buff[offset]);
    offset += 2;
    offset++;        // 告警级别数量

    acmu_para_data.power_off_alarm_level = buff[offset++];                         //交流停电告警属性设置
    acmu_para_data.phase_lost_alarm_level = buff[offset++];                        //交流缺相告警属性设置
    acmu_para_data.phase_volt_unbalance_alarm_level = buff[offset++];              //交流相电压不平衡告警属性设置
    acmu_para_data.volt_high_alarm_level = buff[offset++];                         //交流相电压高告警属性设置
    acmu_para_data.volt_low_alarm_level = buff[offset++];                          //交流相电压低告警属性设置
    acmu_para_data.curr_hign_alarm_level = buff[offset++];                         //交流相电流高告警属性设置
    acmu_para_data.spd_c_alarm_level = buff[offset++];                             //C级防雷器损坏告警属性设置
    acmu_para_data.output_switch_disconnection_alarm_level = buff[offset++];       //交流输出空开断告警属性设置
    
    if(cmd_buf_temp->data_len == OLD_PARA_LEN)
    {
        cmd_buf_temp->rtn = set_acmu_para_data(&acmu_para_data, OLD_MODE);
    }
    else{
        acmu_para_data.obligate_para_s = buff[offset++];                         ///预留公共参数字节数 
        acmu_para_data.ac_line_voltage_max = get_int16_data(&buff[offset]);      ///<交流线电压高阈值
        offset += 2;               
        acmu_para_data.ac_line_voltage_min = get_int16_data(&buff[offset]);      ///<交流线电压低阈值
        offset += 2;
        acmu_para_data.ac_line_voltage_max_alarm_level = buff[offset++];         ///<交流线电压高告警属性设置
        acmu_para_data.ac_line_voltage_min_alarm_level = buff[offset++];         ///<交流线电压低告警属性设置
        cmd_buf_temp->rtn = set_acmu_para_data(&acmu_para_data, NEW_MODE);
    }
    
    return SUCCESSFUL;
}


Static int pack_comm_func(void* dev_inst, void *cmd_buf){
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;
    unsigned char data_flag1 = 0;
    unsigned char data_flag2 = 0;

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(cmd_buf_temp->buf != NULL, FAILURE);
    
    buff = cmd_buf_temp->buf;
    get_one_data(ACMU_DATA_ID_DATA_FLAG1, &data_flag1);
    data_flag1 = data_flag1 | FLAG1_E | FLAG1_F;
    buff[1] = data_flag1;      // data_flag1;

    get_one_data(ACMU_DATA_ID_DATA_FLAG2, &data_flag2);
    buff[2] = data_flag2;   
    if( ((cmd_buf_t*)cmd_buf)->rtn != RTN_OK){
        buff[0] = 1;
        buff[3] = RTN_INVLDATA;
        ((cmd_buf_t*)cmd_buf)->data_len = 4;
        return SUCCESSFUL;
    }
    buff[0] = 0;
    ((cmd_buf_t*)cmd_buf)->data_len = 3;
    return SUCCESSFUL;
}

Static int parse_his_alarm(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    unsigned char* buff;

    RETURN_VAL_IF_FAIL(dev_inst != NULL && cmd_buf_temp != NULL, FAILURE);
    buff = cmd_buf_temp->buf;
    RETURN_VAL_IF_FAIL(buff != NULL, FAILURE);

    s_command_type = (cmd_buf_temp->data_len > 1) ? buff[1] : WRONG_DATA_LENGTH;
    return SUCCESSFUL;
}

Static int check_alm_id(unsigned short alm_code) {

   for (int i = 0; i < sizeof(s_alarm_id) / sizeof(s_alarm_id[0]); i++) {
        if(alm_code == ALM_ID_GET_ALM_CODE(s_alarm_id[i]))
        {
            return SUCCESSFUL;
        }
   }
   return FAILURE;
}

Static int HisAlmToBuff(unsigned short index, cmd_buf_t *cmd_buf) {
    unsigned char* buff = cmd_buf->buf;
    unsigned int offset = 0;
    alarm_map_t* alarm_map = NULL;
    his_alarm_info_t tHisAlm;
    time_base_t start_time, end_time;
    unsigned char uc_ID2, uc_index, alm_level_offset = 0;
    static unsigned char sendlast_flag = FALSE;

    unsigned char data_flag1 = 0;
    unsigned char data_flag2 = 0;
    get_one_data(ACMU_DATA_ID_DATA_FLAG1, &data_flag1);
    get_one_data(ACMU_DATA_ID_DATA_FLAG2, &data_flag2);

    buff[offset++] = 0;       // 长度占位
    buff[offset++] = data_flag1;      // data_flag1
    buff[offset++] = data_flag2;      // data_flag2
    buff[offset++] = GROUP_TYPE_SEND_NORMAL;    // commandtype

    do{
        // 获取历史告警记录
        rt_memset_s(&tHisAlm,sizeof(his_alarm_info_t),0,sizeof(his_alarm_info_t));
        
        if (pub_hisrecord_read_msg(1, 1, index, (unsigned char *)&tHisAlm) != SUCCESSFUL)
        { 
            if(sendlast_flag)// 没有实时告警记录
            {
                data_flag2 &= ~(FLAG2_HISALMOV | FLAG2_HISALM); // 清除历史告警上送标志位
                set_one_data(ACMU_DATA_ID_DATA_FLAG2, &data_flag2);
                buff[OFFSET_DATALEN] = 1; // 长度填充
                buff[OFFSET_FLAG1] = data_flag1;
                buff[OFFSET_FLAG2] = data_flag2;
                buff[OFFSET_ERRID] = RTN_NODATA;
                cmd_buf->rtn = RTN_NODATA;
                cmd_buf->data_len = OFFSET_ERRID + 1;
                return 0;
            }
            buff[OFFSET_COMMANDTYPE] = GROUP_TYPE_SEND_LAST;
            sendlast_flag = TRUE;
            break;
        }

        if (get_alarm_sn(tHisAlm.alarm_id, &alarm_map) == -1 || check_alm_id(tHisAlm.alarm_id) == -1) 
        {
            index++;
            continue;
        }

        sendlast_flag = FALSE;
        uc_ID2 = alarm_map->id2;
        alm_level_offset = GET_ALM_PARA_BY_ALM_CODE(tHisAlm.alarm_id,0);
        uc_index = tHisAlm.index;
        time_t_to_timestruct(tHisAlm.start_time, &start_time);
        time_t_to_timestruct(tHisAlm.end_time, &end_time);

        buff[offset++] = ID1_ACMU_ALARM;     // 告警ID1
        buff[offset++] = uc_ID2;      // 告警ID2
        buff[offset++] = (uc_index > 0) ? (uc_index - 1) : uc_index; // 告警Index
        get_alm_para(alm_level_offset,&buff[offset++]);
        put_time_to_buff(&buff[offset], start_time);  // 告警产生时间
        offset += 7;
        put_time_to_buff(&buff[offset], end_time);  // 告警结束时间
        offset += 7;
        index++;

        if (offset > ACMU_HIS_ALARM_PACK_MAX) {   // 如果数据包太长,分成多个包  
            break;
        }
    }while(1);

    buff[OFFSET_DATALEN] = offset - 3;                     // 长度填充
    cmd_buf->data_len = offset;

    return index;
}


Static int pack_his_alarm(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* cmd_buf_temp = (cmd_buf_t*)cmd_buf;
    static unsigned short s_start_index = 0, s_start_index_bak = 0, s_end_index = 0;

    unsigned char data_flag1 = 0;
    unsigned char data_flag2 = 0;
    unsigned short debug_cmd_cnt = 0;

    // 参数检查
    if (dev_inst == NULL || cmd_buf_temp == NULL || cmd_buf_temp->buf == NULL) {
        return FAILURE;
    }

    get_one_data(ACMU_DATA_ID_GET_HIS_ALM_CNT, &debug_cmd_cnt);
    debug_cmd_cnt++;
    set_one_data(ACMU_DATA_ID_GET_HIS_ALM_CNT, &debug_cmd_cnt);

    // 获取数据标志位
    get_one_data(ACMU_DATA_ID_DATA_FLAG1, &data_flag1);
    get_one_data(ACMU_DATA_ID_DATA_FLAG2, &data_flag2);

    // 处理命令类型
    switch (s_command_type) {
        case GROUP_TYPE_RCV_START:
            s_start_index = 0;
            break;
        case GROUP_TYPE_RCV_NEXT:
            s_start_index = s_end_index;
            break;
        case GROUP_TYPE_RCV_ERROR:
            s_start_index = s_start_index_bak;
            break;
        case GROUP_TYPE_RCV_END:
            data_flag2 &= ~(FLAG2_HISALMOV | FLAG2_HISALM); // 清除历史告警上送标志位
            set_one_data(ACMU_DATA_ID_DATA_FLAG2, &data_flag2);
            cmd_buf_temp->buf[OFFSET_DATALEN] = 1; // 长度填充
            cmd_buf_temp->buf[OFFSET_FLAG1] = data_flag1 | FLAG1_E | FLAG1_F;
            cmd_buf_temp->buf[OFFSET_FLAG2] = data_flag2;
            cmd_buf_temp->buf[OFFSET_COMMANDTYPE] = GROUP_TYPE_SEND_END;
            cmd_buf_temp->data_len = OFFSET_COMMANDTYPE + 1;
            return SUCCESSFUL;
        default:
            cmd_buf_temp->buf[OFFSET_DATALEN] = 1; // 长度填充
            cmd_buf_temp->buf[OFFSET_FLAG1] = data_flag1 | FLAG1_E | FLAG1_F;
            cmd_buf_temp->buf[OFFSET_FLAG2] = data_flag2;
            cmd_buf_temp->buf[OFFSET_ERRID] = RTN_INVLDATA;
            cmd_buf_temp->rtn = RTN_INVLDATA;
            cmd_buf_temp->data_len = OFFSET_ERRID + 1;
            return SUCCESSFUL;
    }

    // 备份起始索引
    s_start_index_bak = s_start_index;

    // 转换历史告警记录到缓冲区
    s_end_index = HisAlmToBuff(s_start_index, cmd_buf_temp);

    cmd_buf_temp->buf[OFFSET_FLAG1] = data_flag1 | FLAG1_E | FLAG1_F;
    return SUCCESSFUL;
}

