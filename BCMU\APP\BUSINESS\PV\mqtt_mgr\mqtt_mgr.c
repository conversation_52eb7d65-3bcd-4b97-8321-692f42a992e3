#include <stdio.h>
#include <stdint.h>
#include <sys/time.h>
#include "mqtt_mgr.h"
#include "mqtt_realdata.h"
#include "mqtt_alarm.h"
#include "mqtt_set_get.h"
#include "mqtt_update.h"
#include "mqtt_upfile.h"
#include "realdata_id_in.h"
#include "realdata_save.h"
#include "utils_rtthread_security_func.h"
#include "mqtt_list.h"
#include "network_crt_parse.h"

static config_value_t g_config_value[MAX_CLIENT] = {0};
static int g_resend_cnt[MAX_CLIENT] = {0};
static passive_cmd_t s_passive_cmd_table[] =
{
    {"/zte/down%s/inv/+/synTime", {0}, handle_syn_time},
    {"/zte/down%s/inv/+/config",  {0}, handle_config},
    {"/zte/down%s/inv/+/set",     {0}, handle_set_data},
    {"/zte/down%s/inv/+/get",     {0}, handle_get_data},
    {"/zte/down%s/inv/+/update",  {0}, handle_update},
    {"/zte/down%s/inv/+/upFile",  {0}, handle_up_file},
};
#define SUB_TOPIC_NUM sizeof(s_passive_cmd_table) / sizeof(s_passive_cmd_table[0])


static int send_time(void* client)
{
    char topic[MQTT_TOPIC_LEN_MAX] = {0};
    char uuid[UUID_LEN] = {0};
    char site_id[SITE_ID_LEN] = {0};
    char device_id[STR_LEN_16] = {0};
    int which = 0;
    cJSON* json_data = cJSON_CreateObject();
    if (json_data == NULL)
    {
        LOG_E("send_time create json error!\n");
        return MQTT_NULL_ERR;
    }

    which = which_client(client);
    gen_uuid(uuid);
    get_site_id(which, site_id);
    get_device_id(get_master_addr() - 1, device_id);
    rt_snprintf_s(topic, MQTT_TOPIC_LEN_MAX,
                    "/zte/up%s/inv/%s/synTime", site_id, device_id);
    cJSON_AddStringToObject(json_data, "msgId", uuid);
    cJSON_AddNumberToObject(json_data, "time", mqtt_get_timestamp());

    send_mqtt_data(client, topic, json_data);
    cJSON_Delete(json_data);
    return MQTT_SUCCESS;
}

int handle_online(void* client)
{
    if (client == NULL)
    {
        return MQTT_NULL_ERR;
    }
    sub_unsub_topic(which_client(client), OPT_SUB);
    for (int idx = 0; idx < PV_NUM; idx++)
    {
        send_on_offline(client, idx, ONLINE);
    }
    send_time(client);
    get_moniter_update_rst();
    send_mqtt_realdata(client);
    send_alarm_when_connected();
    return MQTT_SUCCESS;
}

int handle_syn_time(void* client, message_data_t* msg)
{
    cJSON* json_data = NULL;
    cJSON* json_timestamp = NULL;
    struct timeval tv = {0};
    int cur_dev = 0;
    mqtt_err_e ret = 0;

    if (client == NULL || msg == NULL || msg->message == NULL)
    {
        return MQTT_NULL_ERR;
    }

    ret = parse_msg_sn(msg->topic_name, &cur_dev);
    if (ret != MQTT_SUCCESS || (cur_dev + 1) != get_master_addr())
    {
        return MQTT_SCOPE_ERR;
    }

    json_data = cJSON_Parse(msg->message->payload);
    json_timestamp = cJSON_GetObjectItem(json_data, "time");
    if(json_timestamp == NULL)
    {
        cJSON_Delete(json_data);
        return MQTT_NULL_ERR;
    }

    tv.tv_sec = json_timestamp->valuedouble / 1000;
    cJSON_Delete(json_data);

    if (tv.tv_sec < TIME_STMP_MIN || tv.tv_sec > TIME_STMP_MAX)
    {
        return MQTT_SCOPE_ERR;
    }
    settimeofday(&tv, NULL);

    return MQTT_SUCCESS;
}

int handle_config(void* client, message_data_t* msg)
{
    cJSON* json_para_val = NULL;
    cJSON* para_item = NULL;
    int need_send = FALSE;
    unsigned short u_short_data = 0;
    char uuid[UUID_LEN] = {0};
    char topic[MQTT_TOPIC_LEN_MAX] = {0};
    char site_id[SITE_ID_LEN] = {0};
    char devide_id[STR_LEN_16] = {0};
    char *value_str = NULL;
    mqtt_err_e ret = 0;
    int which = 0;

    if (client == NULL || msg == NULL || msg->message == NULL)
    {
        return MQTT_NULL_ERR;
    }

    which = which_client(client);
    if (which == MAX_CLIENT)
    {
        return MQTT_SCOPE_ERR;
    }

    ret = parse_msg_sn(msg->topic_name, NULL);
    if (ret != MQTT_SUCCESS)
    {
        return MQTT_SCOPE_ERR;
    }

    json_para_val = mqtt_parse_msg(msg->message->payload, uuid);
    if (NULL == json_para_val)
    {
        return MQTT_NULL_ERR;
    }

    // 比较IP是否变更
    value_str = cJSON_GetStringValue(cJSON_GetObjectItem(json_para_val, "serverIp"));
    if (value_str != NULL)
    {
        if (rt_strncmp(value_str, g_config_value[which].server_ip, STR_LEN_32) == 0)
        {
            cJSON_DeleteItemFromObject(json_para_val, "serverIp");
        }
        else
        {
            need_send = TRUE;
            set_one_para(DAC_PARA_ID_MQTT_NET_IP_OFFSET + which, value_str, TRUE, TRUE);
        }
    }

    // 比较端口是否变更
    para_item = cJSON_GetObjectItem(json_para_val, "serverPort");
    if (para_item != NULL)
    {
        u_short_data = cJSON_GetNumberValue(para_item);
        if (u_short_data == g_config_value[which].server_port)
        {
            cJSON_DeleteItemFromObject(json_para_val, "serverPort");
        }
        else
        {
            need_send = TRUE;
            set_one_para(DAC_PARA_ID_MQTT_NET_PORT_OFFSET + which, &u_short_data, TRUE, TRUE);
        }
    }

    if (need_send)
    {
        get_site_id(which, site_id);
        get_device_id(get_master_addr() - 1, devide_id);
        rt_snprintf_s(topic, MQTT_TOPIC_LEN_MAX, "/zte/up%s/inv/%s/config", site_id, devide_id);
        para_item = cJSON_GetObjectItem(json_para_val, "time");
        cJSON_SetNumberValue(para_item, mqtt_get_timestamp());
        send_mqtt_data(client, topic, json_para_val);
        save_numeric_para();
        save_string_para();
    }

    cJSON_Delete(json_para_val);
    return MQTT_SUCCESS;
}

int set_process_site_id(int which, char* site_id)
{
    char cur_site_id[SITE_ID_LEN] = {"/"};
    if (rt_strnlen(site_id, STR_LEN_16) > 0)
    {
        if (site_id[0] == '/')
        {
            rt_strncpy_s(cur_site_id, SITE_ID_LEN, site_id, STR_LEN_16);
        }
        else
        {
            rt_strncpy_s(&cur_site_id[1], SITE_ID_LEN - 1, site_id, STR_LEN_16 - 1);
        }
    }
    set_site_id(which, cur_site_id);
    rt_kprintf("site_id: %s\n",cur_site_id);
    return SUCCESSFUL;
}

void init_config_para(void)
{
    for (int idx = 0; idx < MAX_CLIENT; idx++)
    {
        get_one_para(DAC_PARA_ID_SITE_ID_OFFSET + idx, g_config_value[idx].site_id);
        get_one_para(DAC_PARA_ID_MQTT_NET_IP_OFFSET + idx, g_config_value[idx].server_ip);
        get_one_para(DAC_PARA_ID_MQTT_NET_PORT_OFFSET + idx, &g_config_value[idx].server_port);
        set_process_site_id(idx, g_config_value[idx].site_id);
    }
}

static void get_config_para(int which, config_value_t* config_value)
{
    get_one_para(DAC_PARA_ID_SITE_ID_OFFSET + which, config_value->site_id);
    get_one_para(DAC_PARA_ID_MQTT_NET_IP_OFFSET + which, config_value->server_ip);
    get_one_para(DAC_PARA_ID_MQTT_NET_PORT_OFFSET + which, &config_value->server_port);
}

int is_config_change(int which)
{
    config_value_t config_value = {0};
    char topic[MQTT_TOPIC_LEN_MAX] = {0};
    char site_id[SITE_ID_LEN] = {0};
    char device_id[STR_LEN_16] = {0};
    char uuid[UUID_LEN] = {0};
    cJSON* json_data = NULL;
    int is_change = RT_FALSE;
    mqtt_client_t* client = get_mqtt_client(which);

    RETURN_VAL_IF_FAIL(client != NULL, RT_FALSE);
    get_config_para(which, &config_value);
    if(rt_memcmp(&g_config_value[which], &config_value, sizeof(config_value_t)) == 0)
    {
        return RT_FALSE;
    }

    gen_uuid(uuid);
    get_site_id(which, site_id);
    get_device_id(get_master_addr() - 1, device_id);

    json_data = cJSON_CreateObject();
    cJSON_AddStringToObject(json_data, "msgId", uuid);
    cJSON_AddNumberToObject(json_data, "time", mqtt_get_timestamp());

    if(rt_memcmp(g_config_value[which].server_ip, config_value.server_ip, sizeof(str32)) != 0)
    {
        is_change = RT_TRUE;
        cJSON_AddStringToObject(json_data, "serverIp", config_value.server_ip);
    }

    if(g_config_value[which].server_port != config_value.server_port)
    {
        is_change = RT_TRUE;
        cJSON_AddNumberToObject(json_data, "serverPort", config_value.server_port);
    }

    // 比较站点id
    if(rt_memcmp(g_config_value[which].site_id, config_value.site_id, sizeof(str16)) != 0)
    {
        set_process_site_id(which, config_value.site_id);
        is_change = RT_TRUE;
    }

    rt_memcpy(&g_config_value[which], &config_value, sizeof(config_value_t));
    rt_snprintf_s(topic, MQTT_TOPIC_LEN_MAX,
                    "/zte/up%s/inv/%s/config", site_id, device_id);
    send_mqtt_data(client, topic, json_data);
    cJSON_Delete(json_data);
    return is_change;
}


int topic_check(mqtt_client_t* client, char* topic_filter)
{   
    mqtt_list_t *curr, *next;
    message_handlers_t *msg_handler;

    if((client == NULL) || (client->mqtt_msg_handler_list.prev == NULL) || (client->mqtt_msg_handler_list.next == NULL))
    {
        return FALSE;
    }

    LIST_FOR_EACH_SAFE(curr, next, &client->mqtt_msg_handler_list) {
        msg_handler = LIST_ENTRY(curr, message_handlers_t, list);
        if(NULL != msg_handler->topic_filter)
        {
            if(0 == rt_strcmp(msg_handler->topic_filter, topic_filter))
            {
                return TRUE;
            }
        }
    }
    return FALSE;
}


void sub_unsub_topic(int which_client, int opt)
{   
    static unsigned int count[MAX_CLIENT] = {0};
    int rtn = -1;
    char site_id[SITE_ID_LEN] = {0};
    static char sub_topic[MAX_CLIENT][SUB_TOPIC_NUM][MQTT_TOPIC_LEN_MAX] = {0};
    mqtt_client_t* client = get_mqtt_client(which_client);

    RETURN_IF_FAIL(client != NULL);

    // 只有订阅的时候才计数
    if(opt == OPT_SUB)
    {
        if(count[which_client] % MQTT_DETECT_COUNT != 0)
        {   
            count[which_client]++;
            return;
        }
        count[which_client] = 1;
    }

    get_site_id(which_client, site_id);
    for (int idx = 0; idx < SUB_TOPIC_NUM; idx++)
    {
        if (opt == OPT_UNSUB)
        {
            s_passive_cmd_table[idx].is_subscribed[which_client] = FALSE;
            continue;
        }
        
        rt_memset_s(sub_topic[which_client][idx], MQTT_TOPIC_LEN_MAX, 0, MQTT_TOPIC_LEN_MAX);
        rt_snprintf_s(sub_topic[which_client][idx], MQTT_TOPIC_LEN_MAX, s_passive_cmd_table[idx].topic_template, site_id);
        // 检查每个主题是否订阅成功
        if(!topic_check(client, sub_topic[which_client][idx]))
        {
            s_passive_cmd_table[idx].is_subscribed[which_client] = FALSE;
        }

        if (!s_passive_cmd_table[idx].is_subscribed[which_client])
        {
            rtn = mqtt_subscribe(client, sub_topic[which_client][idx], QOS0, s_passive_cmd_table[idx].handler);
            if (rtn == MQTT_SUCCESS_ERROR)
            {
                s_passive_cmd_table[idx].is_subscribed[which_client] = TRUE;
            }
        }
    }
}

static void set_will_msg(int which)
{
    static char will_topic[MQTT_TOPIC_LEN_MAX] = {0};
    static char will_data[WILL_DATA_LEN] = {0};
    char site_id[SITE_ID_LEN] = {0};
    char device_id[STR_LEN_16] = {0};
    char uuid[UUID_LEN] = {0};
    mqtt_client_t* client = get_mqtt_client(which);

    gen_uuid(uuid);
    get_site_id(which, site_id);
    get_device_id(get_master_addr() - 1, device_id);
    rt_snprintf_s(will_topic, MQTT_TOPIC_LEN_MAX,
                    "/zte/up%s/inv/%s/online", site_id, device_id);
    rt_snprintf_s(will_data, WILL_DATA_LEN, "{\"msgId\":\"%s\",\"time\":0,\"status\":0}", uuid);

    mqtt_set_will_flag(client, 1);
    mqtt_set_will_options(client, will_topic, QOS0, 0, will_data);
}

int close_mqtt_client(_rt_msg_t curr_msg)
{
    char which_tls = *(char*)(curr_msg->msg.data);
    if (get_tls_conf(which_tls) == NULL)
    {
        return SUCCESSFUL;
    }
    close_client(which_tls);
    nettype_tls_cert_free(which_tls);
    nettype_tls_conf_free(which_tls);
    g_resend_cnt[(int)which_tls] = 0;
    LOG_E("close_mqtt_client: %d\n",which_tls);
    return SUCCESSFUL;
}

void handle_cert_chg_msg(_rt_msg_t curr_msg)
{   
    if(curr_msg->msg.data_size == 0)
    {
        // 数据大小为0，只刷新证书时间
        crt_date_parse();
    }
    else
    {
        close_mqtt_client(curr_msg);
    }
}

int is_need_close_conn(void)
{
    char sn[STR_LEN_32] = {0};
    char cur_device_id[SN_LEN] = {0};
    unsigned char cur_addr = 0;
    unsigned char mode = get_mode_addr(&cur_addr);

    // 从机不连接
    if (mode == SLAVE)
    {
        return RT_TRUE;
    }

    // 条码不正确不连接
    get_one_para(DAC_PARA_ID_MACHINE_BARCODE_OFFSET, sn);
    if (rt_strnlen(sn, STR_LEN_32) != SN_LEN)
    {
        return RT_TRUE;
    }

    set_one_data(DAC_DATA_ID_MASTER_SN, sn);
    set_one_data(DAC_PO_DATA_ID_MASTER_SN, sn);

    get_device_id(cur_addr - 1, cur_device_id);
    if (get_master_addr() != cur_addr ||
        rt_strncmp(cur_device_id, sn, SN_LEN) != 0)
    {
        set_master_addr(cur_addr);
        set_device_id(cur_addr - 1, sn, SN_LEN);
        return RT_TRUE;
    }
    return RT_FALSE;
}

void close_client(int which)
{
    mqtt_client_t* client = get_mqtt_client(which);
    if (client != NULL && CLIENT_STATE_CONNECTED == client->mqtt_client_state)
    {
        mqtt_disconnect(client);
        rt_thread_mdelay(500);
    }
}

void manage_connect(void)
{
    // 非静态时，断链重连会数据异常，导致连接失败
    static char s_port[MAX_CLIENT][STR_LEN_16] = {0};
    static char s_client_id[STR_LEN_16] = {0};
    static int count[MAX_CLIENT] = {MQTT_RECONN_COUNT/2, MQTT_RECONN_COUNT/2};
    char cur_device_id[STR_LEN_16] = {0};

    get_device_id(get_master_addr() - 1, cur_device_id);

    for (int client_idx = 0; client_idx < MAX_CLIENT; client_idx++)
    {
        mqtt_client_t* client = get_mqtt_client(client_idx);
        if (is_config_change(client_idx) == RT_TRUE)
        {
            g_resend_cnt[client_idx] = 0;
            close_client(client_idx);
            continue;
        }

        if (client->mqtt_client_state == CLIENT_STATE_CONNECTED)
        {   
            g_resend_cnt[client_idx] = 0;
            sub_unsub_topic(client_idx, OPT_SUB);
            continue;
        }

        if (rt_strnlen(g_config_value[client_idx].server_ip, STR_LEN_32) < MIN_IP_LEN)
        {
            continue;
        }

        if (g_config_value[client_idx].server_port < 22)
        {
            continue;
        }
        rt_snprintf_s(s_port[client_idx], STR_LEN_16, "%d", g_config_value[client_idx].server_port);
        mqtt_set_host(client, g_config_value[client_idx].server_ip);
        mqtt_set_port(client, s_port[client_idx]);
        rt_snprintf_s(s_client_id, STR_LEN_16, "Dev%s", cur_device_id);
        mqtt_set_client_id(client, s_client_id);
        sub_unsub_topic(client_idx, OPT_UNSUB);
        set_will_msg(client_idx);
        if(count[client_idx] % MQTT_RECONN_COUNT == 0)
        {
            if(g_resend_cnt[client_idx]++ < MQTT_RESEND_COUNT)
            {
                mqtt_connect(client);
                count[client_idx] = 1;
                handle_inform_4g_reset_device();
                rt_kprintf("re connect %d  resend_cnt: %d\n",client_idx, g_resend_cnt[client_idx]);
            }
            else if(g_resend_cnt[client_idx] >= MQTT_RESEND_THRESH) // 1个小时
            {
                g_resend_cnt[client_idx] = 0;
                handle_inform_4g_reset_device();
                LOG_E("%s:%d| resend_cnt 0", __FUNCTION__ , __LINE__);
            }
        }
        count[client_idx]++;
        rt_thread_mdelay(20);
    }
}