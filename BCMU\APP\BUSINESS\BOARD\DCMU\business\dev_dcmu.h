#ifndef _DEVICE_DCMU_H
#define _DEVICE_DCMU_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include "drv_utils.h"

/*-----设备类型-----*/
#define DEV_CSU        1
#define DEV_DCMU       2
#define DEV_NORTH_1104      3
#define DEV_NORTH_1363      4
#define DEV_REMOTE_DOWNLOAD     5
#define DEV_BOTTOM_COMM_UPDATE_UPLOAD  6
#define DEV_NORTH_DCMU_APPTEST  7
#define DEV_NORTH_DCMU_CAN0  8

#define DCMU_BASE_ADDR 0x60

#ifdef UNITEST
static int mock_addr = 0XFF;
#define BOOT_DATA_ADDRESS                 (&mock_addr)
#else
#define BOOT_DATA_ADDRESS       0x08007800
#endif

#define DCEMDEVICES    "DTSD3366D-A"
#define SWITCH_NUM     24
#define RELAY_NUM      4
#define TEMPERATURE_SENSOR_NUM    1
#define HUMIDITY_SENSOR_NUM       1
#define DEVICES    "ZXDU88 S402 DCMU"
#define CORPERATION_NAME   "ZTE Corporation"
#define MAJOR_VER  1
#define MINOR_VER  0
#define LEN_RELAYNAME 13

/* 设备缓冲区长度 */
#define R_BUFF_LEN          2048       ///<  接收缓冲区长度
#define S_BUFF_LEN          2048       ///<  发送缓冲区长度

#define VER_20              0x20       ///<  1104协议2.0版本号

#define UNKNOW_MODE         0
#define OLD_MODE            1
#define NEW_MODE            2

#define BATT_NUM            4           // 蓄电池组数
#define LOAD_NUM            24          // 检测负载电流路数
#define FUSE_NUM            LOAD_NUM    // 检测负载熔丝路数，与负载电流相同

//告警级别定义
#define ALARMCLASS_MASK     0           // 屏蔽
#define ALARMCLASS_CRITICAL 1           // 紧急
#define ALARMCLASS_MAJOR    2           // 重要
#define ALARMCLASS_MINOR    3           // 一般
#define ALARMCLASS_WARNING  4           // 轻微

#define TEMPERATURE_MAX     100      // 最高温度阈值
#define TEMPERATURE_MIN     -40      // 最低温度阈值
#define DEFAULT_TEMP_MIN    -55.0f   // 缺省温度最小值
#define DEFAULT_TEMP_MAX    250.0f   // 缺省温度最大值
#define HUMIDITY_MAX        100      // 最高湿度阈值
#define HUMIDITY_MIN         0       // 最低湿度阈值

#define FLAG1_ALMCH             0x01  // 实时告警变化标志
#define FLAG1_PARACH            0x02  // 可设参数变化标志
#define FLAG1_TIMECH            0x08  // 时间重置标志
#define FLAG1_F                 0x40  // 报文组结束标志F
#define FLAG1_E                 0x80  // 报文结束标志E
#define FLAG2_HISALMOV          0x01  // 历史告警溢出标志
#define FLAG2_RST               0x40  // 复位标志
#define FLAG2_HISALM            0x80  // 有未读取历史告警标志

typedef enum {
    PROTOCOL_NORTH_INDEX = 0,
    PROTOCOL_1104_INDEX,
    PROTOCOL_1363_INDEX,
    PROTOCOL_DOWNLOAD_INDEX,
    PROTOCOL_BOTTOM_INDEX,
    PROTOCOL_INDEX_MAX,
} dev_protocol_index_e;

typedef enum {
    COMM_BAUD_RATE_1200 = 0,
    COMM_BAUD_RATE_2400,
    COMM_BAUD_RATE_4800,
    COMM_BAUD_RATE_9600,
    COMM_BAUD_RATE_19200,
    COMM_BAUD_RATE_38400,
    COMM_BAUD_RATE_NUM
}dcmu_comm_baudrate_type_e;

typedef struct boot_info
{
    char ver[16];
    char date[16];
} boot_info_t;

typedef struct {
    float dc_output_voltage;                ///<直流输出电压
    float total_load_current;               ///<负载总电流
    float battery_voltage[BATT_NUM];        ///<电池电压1
    float battery_current[BATT_NUM];        ///<电池电流1
    float battery_temperature[BATT_NUM];    ///<电池温度1
    float branch_current[SWITCH_NUM];       ///<分路电流1
    float branch_battery[SWITCH_NUM];       ///<分路电量1
} dcem_show_data_t;

int dcmu_update_baudrate(void);
int dcmu_update_host_address(void);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _DEVICE_DCMU_H