/*!
    \file    gd32f4xx_it.c
    \brief   interrupt service routines
    
    \version 2022-05-26, V2.0.0, demo for GD32F4xx
*/

/*
    Copyright (c) 2022, GigaDevice Semiconductor Inc

    Redistribution and use in source and binary forms, with or without modification, 
are permitted provided that the following conditions are met:

    1. Redistributions of source code must retain the above copyright notice, this 
       list of conditions and the following disclaimer.
    2. Redistributions in binary form must reproduce the above copyright notice, 
       this list of conditions and the following disclaimer in the documentation 
       and/or other materials provided with the distribution.
    3. Neither the name of the copyright holder nor the names of its contributors 
       may be used to endorse or promote products derived from this software without 
       specific prior written permission.

    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" 
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED 
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. 
IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, 
INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT 
NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR 
PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, 
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) 
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY 
OF SUCH DAMAGE.
*/


#include "gd32f4xx_it.h"
#include "systick.h"
#include "string.h"
#include "main.h"

// 看门狗中断喂狗配置
#define WATCHDOG_IN_INTERRUPT_ENABLED   1    // 启用中断喂狗
#define WDI_PIN_PORT                    GPIOE
#define WDI_PIN_NUM                     GPIO_PIN_2
#define WATCHDOG_FEED_INTERVAL_MS       10   // 喂狗间隔10ms
#include "flash.h"
/*!
    \brief      this function handles NMI exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void NMI_Handler(void)
{
}

/*!
    \brief      this function handles HardFault exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void HardFault_Handler(void)
{
    /* if Hard Fault exception occurs, go to infinite loop */
    while (1){
    }
}

/*!
    \brief      this function handles MemManage exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void MemManage_Handler(void)
{
    /* if Memory Manage exception occurs, go to infinite loop */
    while (1){
    }
}

/*!
    \brief      this function handles BusFault exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void BusFault_Handler(void)
{
    /* if Bus Fault exception occurs, go to infinite loop */
    while (1){
    }
}

/*!
    \brief      this function handles UsageFault exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void UsageFault_Handler(void)
{
    /* if Usage Fault exception occurs, go to infinite loop */
    while (1){
    }
}

/*!
    \brief      this function handles SVC exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void SVC_Handler(void)
{
}

/*!
    \brief      this function handles DebugMon exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void DebugMon_Handler(void)
{
}

/*!
    \brief      this function handles PendSV exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void PendSV_Handler(void)
{
}

/*!
    \brief      this function handles SysTick exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
extern uint16_t wTimeout;
extern uint8_t communication_interrupt_flag;
void SysTick_Handler(void)
{
    static uint16_t wBntCnt=0;
    static uint16_t watchdog_counter = 0;

    delay_decrement();

#if WATCHDOG_IN_INTERRUPT_ENABLED
    // 看门狗喂狗逻辑 - 每10ms执行一次
    if (++watchdog_counter >= WATCHDOG_FEED_INTERVAL_MS) {
        watchdog_counter = 0;
        // 执行硬件看门狗喂狗操作
        gpio_bit_write(WDI_PIN_PORT, WDI_PIN_NUM, RESET);  // 拉低
        gpio_bit_write(WDI_PIN_PORT, WDI_PIN_NUM, SET);    // 拉高
    }
#endif

    static int LedCnt = 0;
    LedCnt = 500;
    if (0 == uwTick%LedCnt)
    {
        // gpio_bit_write(GPIOD, GPIO_PIN_14,SET);   /*RUN : PD14 */
        // gpio_bit_write(GPIOD, GPIO_PIN_15,SET);   /*ALM : PD15 */
        // gpio_bit_write(GPIOE, GPIO_PIN_2, SET);   /*SOC1 : PE2 */
        // gpio_bit_write(GPIOE, GPIO_PIN_3, SET);   /*SOC2 : PE3 */
        // gpio_bit_write(GPIOE, GPIO_PIN_4, SET);   /*SOC3 : PE4 */
        // gpio_bit_write(GPIOE, GPIO_PIN_5, SET);   /*SOC4 : PE5 */
    }
    else if (LedCnt/2 == uwTick%LedCnt && wBntCnt<1000)     //按键1s后指示灯全灭
    {
        // gpio_bit_write(GPIOD,GPIO_PIN_14, RESET); /*RUN : PD14 */
        // gpio_bit_write(GPIOD,GPIO_PIN_15, RESET); /*ALM : PD15 */
        // gpio_bit_write(GPIOE, GPIO_PIN_2, RESET); /*SOC1 : PE2 */
        // gpio_bit_write(GPIOE, GPIO_PIN_3, RESET); /*SOC2 : PE3 */
        // gpio_bit_write(GPIOE, GPIO_PIN_4, RESET); /*SOC3 : PE4 */
        // gpio_bit_write(GPIOE, GPIO_PIN_5, RESET); /*SOC4 : PE5 */
    }
    if (wTimeout>1 && SET==gpio_input_bit_get(GPIOE, GPIO_PIN_15)) //通信中断按键关机
    {
        if (wBntCnt < 2000)
        {
            wBntCnt++;
        }
    }
    else if (wBntCnt >= 1000 && wTimeout>1)
    {
        gpio_bit_write(GPIOF, GPIO_PIN_2, RESET);       /*关机 */
    }
    else
    {
        wBntCnt = 0;
    }
}

extern uint8_t g_rs485_rxBuff[500];
extern uint32_t remain_cnt;
void USART1_IRQHandler(void)
{
    uint32_t recv_len = 0;
    uint32_t data = 0;
    if ((usart_flag_get(USART1, USART_FLAG_IDLE) != RESET)
             && (usart_interrupt_flag_get(USART1, USART_INT_FLAG_IDLE) != RESET)) 
    {
        usart_flag_clear(USART1, USART_FLAG_IDLE);
        usart_interrupt_flag_clear(USART1, USART_INT_FLAG_IDLE);
        USART_STAT0(USART1);
        USART_DATA(USART1);
        dma_channel_disable(DMA0, DMA_CH5);
        
        recv_len = sizeof(g_rs485_rxBuff) - dma_transfer_number_get(DMA0, DMA_CH5);
        if (recv_len != 0 && recv_len < sizeof(g_rs485_rxBuff))
        {
            remain_cnt = recv_len;
            dma_memory_address_config(DMA0, DMA_CH5, DMA_MEMORY_0, (uint32_t)g_rs485_rxBuff);
            dma_transfer_number_config(DMA0, DMA_CH5, sizeof(g_rs485_rxBuff));
            UART_RxSrv(remain_cnt , g_rs485_rxBuff);
            dma_flag_clear(DMA0, DMA_CH5, DMA_FLAG_FTF);
            dma_channel_enable(DMA0, DMA_CH5);
        }
        else
        {
            remain_cnt = 0;
            memset(g_rs485_rxBuff, 0, sizeof(g_rs485_rxBuff));
            usart_data_receive(data);
            dma_memory_address_config(DMA0, DMA_CH5, DMA_MEMORY_0, (uint32_t)g_rs485_rxBuff);
            dma_transfer_number_config(DMA0, DMA_CH5, sizeof(g_rs485_rxBuff));
            dma_flag_clear(DMA0, DMA_CH5, DMA_FLAG_FTF);
            dma_channel_enable(DMA0, DMA_CH5);
        }
        
    }

    return;
}

void USART2_IRQHandler(void)
{
    uint32_t recv_len = 0;
    uint32_t data = 0;
    if ((usart_flag_get(USART2, USART_FLAG_IDLE) != RESET)
             && (usart_interrupt_flag_get(USART2, USART_INT_FLAG_IDLE) != RESET)) 
    {
        usart_flag_clear(USART2, USART_FLAG_IDLE);
        usart_interrupt_flag_clear(USART2, USART_INT_FLAG_IDLE);
        USART_STAT0(USART2);
        USART_DATA(USART2);
        dma_channel_disable(DMA0, DMA_CH1);
        
        recv_len = sizeof(g_rs485_rxBuff) - dma_transfer_number_get(DMA0, DMA_CH1);
        if (recv_len != 0 && recv_len < sizeof(g_rs485_rxBuff))
        {
            remain_cnt = recv_len;
            dma_memory_address_config(DMA0, DMA_CH1, DMA_MEMORY_0, (uint32_t)g_rs485_rxBuff);
            dma_transfer_number_config(DMA0, DMA_CH1, sizeof(g_rs485_rxBuff));
            UART_RxSrv(remain_cnt , g_rs485_rxBuff);
            dma_flag_clear(DMA0, DMA_CH1, DMA_FLAG_FTF);
            dma_channel_enable(DMA0, DMA_CH1);
        }
        else
        {
            remain_cnt = 0;
            memset(g_rs485_rxBuff, 0, sizeof(g_rs485_rxBuff));
            usart_data_receive(data);
            dma_memory_address_config(DMA0, DMA_CH1, DMA_MEMORY_0, (uint32_t)g_rs485_rxBuff);
            dma_transfer_number_config(DMA0, DMA_CH1, sizeof(g_rs485_rxBuff));
            dma_flag_clear(DMA0, DMA_CH1, DMA_FLAG_FTF);
            dma_channel_enable(DMA0, DMA_CH1);
        }
        
    }

    return;
}






