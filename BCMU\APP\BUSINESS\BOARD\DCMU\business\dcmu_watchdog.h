#ifndef WATCHDOG_MANAGER_H
#define WATCHDOG_MANAGER_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include <rtthread.h>
#include <rtdevice.h>
#include "drv_common.h"

// 硬件配置参数（ADM76SARZ特性）
#define WDI_PIN              GET_PIN(E, 2)       // WDI控制引脚（根据实际硬件修改）
#define WATCHDOG_MIN_TIMEOUT_MS 1200             // 芯片最小复位时间（1.2s）

// 喂狗信号时序参数
#define WDI_PERIOD_MS        20                 // 信号周期（20ms）

int set_watchdog_monitor_feed(unsigned char feed_state);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif
