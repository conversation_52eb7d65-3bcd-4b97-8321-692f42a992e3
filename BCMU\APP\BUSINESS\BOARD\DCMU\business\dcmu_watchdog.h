#ifndef WATCHDOG_MANAGER_H
#define WATCHDOG_MANAGER_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

#include <rtthread.h>
#include <rtdevice.h>
#include "drv_common.h"

// 硬件配置参数（ADM76SARZ特性）
#define WDI_PIN              GET_PIN(E, 2)       // WDI控制引脚（根据实际硬件修改）
#define WATCHDOG_MIN_TIMEOUT_MS 1200             // 芯片最小复位时间（1.2s）

// 喂狗信号时序参数
#define WDI_PERIOD_MS        20                 // 信号周期（20ms）

// 中断喂狗配置
#define WATCHDOG_IN_INTERRUPT_ENABLED   1       // 启用中断喂狗

// 函数声明
int set_watchdog_monitor_feed(unsigned char feed_state);
int watchdog_start_thread_backup(void);
int watchdog_stop_thread_backup(void);
void watchdog_print_status(void);

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif
