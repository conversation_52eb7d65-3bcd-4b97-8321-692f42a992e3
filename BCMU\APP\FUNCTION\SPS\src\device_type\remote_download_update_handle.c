#include <string.h>
#include <rtthread.h>
#include <stdlib.h>
#include <fal.h>
#include <fcntl.h>
#include "remote_download_update_handle.h"
#include "remote_download_update_cmd.h"
#include "device_type.h"
#include "sps.h"
#include "cmd.h"
#include "protocol_layer.h"
#include "utils_data_transmission.h"
#include "utils_data_type_conversion.h"
#include "utils_string.h"
#include "partition_def.h"
#include "utils_rtthread_security_func.h"
#include "log_mgr_api.h"
#include "protocol_north_modbus.h"
#include "msg_id.h"
// 操作记录保存回调指针

Static save_action_record_cb_t s_save_action_record_cb = NULL;

short register_save_action_record_cb(save_action_record_cb_t cb) {
    s_save_action_record_cb = cb;
    return SUCCESSFUL; 
}


Static send_msg_to_led_cb_t s_send_msg_to_led_cb = NULL;

short register_send_msg_to_led_cb(send_msg_to_led_cb_t cb) {
    s_send_msg_to_led_cb = cb;
    return SUCCESSFUL; 
}

#define ZK_NAME "CCLIB.BIN"
unsigned char g_zk_update_flag = FALSE;
unsigned int g_download_total_size = 0;

update_file_manage_t s_download_trsdata_ctr_inf;
T_FileUploadStruct s_upload_trsdata_ctr_inf;
unsigned short g_extend_file_crc = 0;  // 扩展帧传输下来的crc

download_trig_ctr_inf_t s_download_trig_ctr_inf;
rt_timer_t  s_trig_timer = NULL;
rt_timer_t  s_transfer_timer = NULL;

unsigned char g_first_frame_flag = FALSE;

cur_download_file_info_t s_cur_download_file_info = {0};
download_file_name_info_t* s_register_download_file_name[MAX_DOWNLOAD_FILE_NUM] = {0};

static unsigned char trig_frame_ack[] = {
    0x30,0x31,0x34,0x30,0x45,0x45,0x35,
    0x36,0x35,0x42,0x41,0x36,0x41,0x42
};

/* Started by AICoder, pid:zb805e4efbh1c64147c208a730eb3c14c48484f7 */
static cmd_handle_register_t s_download_update_cmd_handle[] = {
    {DEV_REMOTE_DOWNLOAD, DOWNLOAD_DATA_DOWNLOAD,         CMD_TYPE_NO_POLL, parse_update_data, pack_update_data},
    {DEV_REMOTE_DOWNLOAD, DOWNLOAD_DATA_DOWNLOAD_EXTEND,  CMD_TYPE_NO_POLL, parse_update_data, pack_update_data},
    {DEV_REMOTE_DOWNLOAD, DOWNLOAD_VERSION_CMP,           CMD_TYPE_NO_POLL, parse_ver_cmp, pack_ver_cmp },
    {DEV_REMOTE_DOWNLOAD, DOWNLOAD_STOP_UPDATE,           CMD_TYPE_NO_POLL, parse_stop_update , pack_stop_update},
    {DEV_REMOTE_DOWNLOAD, DOWNLOAD_GET_FLAG,              CMD_TYPE_NO_POLL, parse_get_flag ,pack_get_flag },
    {DEV_REMOTE_DOWNLOAD, DOWNLOAD_GET_UPDATE_PROG,       CMD_TYPE_NO_POLL, parse_update_progress , pack_update_progress},
    {DEV_REMOTE_DOWNLOAD, DOWNLOAD_DATA_TRIG,             CMD_TYPE_NO_POLL, parse_update_trig, pack_update_trig},
    {DEV_REMOTE_DOWNLOAD, DOWNLOAD_GET_FRAME_MAX_LEN,     CMD_TYPE_NO_POLL, parse_get_single_frame_max_length, pack_get_single_frame_max_length},
    {DEV_REMOTE_DOWNLOAD, DOWNLOAD_SET_FRAME_MAX_LEN,     CMD_TYPE_NO_POLL, parse_set_single_frame_max_length, pack_set_single_frame_max_length},
    {DEV_REMOTE_DOWNLOAD, UPLOAD_FILE_LIST,               CMD_TYPE_NO_POLL, parse_upload_file_list, pack_upload_file_list},
    {DEV_REMOTE_DOWNLOAD, UPLOAD_FILE_DATA,               CMD_TYPE_NO_POLL, parse_upload_file_data, pack_upload_file_data},
    {0} // 结束符
};
/* Ended by AICoder, pid:zb805e4efbh1c64147c208a730eb3c14c48484f7 */

/* Started by AICoder, pid:i8582o05d71be1814562096880d4a2131b45ae95 */
int register_download_file_name(download_file_name_info_t* download_file_name, int download_file_num,  update_info_save_t* update_info_save)
{
    if(download_file_num > MAX_DOWNLOAD_FILE_NUM)
    {
        LOG_E("download_file_num is %d over %d\n", download_file_num, MAX_DOWNLOAD_FILE_NUM);       
        return FAILURE;
    }

    // 将结构体数组转换为指针数组
    for (int i = 0; i < download_file_num; i++) {
        s_register_download_file_name[i] = &download_file_name[i];
    }
    s_cur_download_file_info.cur_store_info_name = update_info_save->update_info_part_name;
    s_cur_download_file_info.cur_store_info_offset =  update_info_save->offset;
    s_cur_download_file_info.feed_dog = update_info_save->feed_dog;
    return SUCCESSFUL;
}
/* Ended by AICoder, pid:i8582o05d71be1814562096880d4a2131b45ae95 */


/* Started by AICoder, pid:a8259jce40a414f1423d0b88b0b8802ef8066ba2 */
/* 解析上传文件列表帧 首发帧和其他帧DATA均为空 */
int parse_upload_file_list(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = NULL;
    T_FileUploadStruct* trsdata_ctr_inf = NULL;
    download_cmd_head_t* proto_head = NULL;
    trsdata_ctr_inf = &s_upload_trsdata_ctr_inf;

    //入参校验
    if ( cmd_buf == NULL || ((cmd_buf_t*)cmd_buf)->buf == NULL)
    {
        return FAILURE;
    }

    tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    //判断协议层解析数据是否正确
    if (tran_cmd_buf->rtn != DOWNLOAD_FRAME_CORRECT)
    {
        return FAILURE;
    }
    proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->req_head;
    trsdata_ctr_inf->cur_frame_no = proto_head->fn2;
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;

    return SUCCESSFUL;
}
/* Ended by AICoder, pid:a8259jce40a414f1423d0b88b0b8802ef8066ba2 */

/* Started by AICoder, pid:7164bof7c0x863614a690ba900d42c4d6340f340 */
/* 打包上传文件列表帧 打包首发帧和其他帧 */
int pack_upload_file_list(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    unsigned short frm_no = 0;
    download_cmd_head_t* proto_head = NULL;

    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL)
    {
        return FAILURE;
    }

    //判断协议层解析数据是否正确
    if (tran_cmd_buf->rtn != DOWNLOAD_FRAME_CORRECT)
    {
        return FAILURE;
    }
    
    proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->req_head;
    frm_no = proto_head->fn2;
        
    if (frm_no == 0)
    {
        trig_end();
        pack_filelist_first_frame(tran_cmd_buf, frm_no);
    }
    else
    {
        pack_filelist_other_frame(tran_cmd_buf, frm_no);
    }
    
    if (s_upload_trsdata_ctr_inf.tFileList.wListTotalFrame ==  proto_head->fn2 &&
        s_upload_trsdata_ctr_inf.tFileList.wListTotalFrame != 0)
    {
        rt_memset_s(&s_upload_trsdata_ctr_inf, sizeof(T_FileUploadStruct), 0, sizeof(T_FileUploadStruct));
    }

    return SUCCESSFUL;
}
/* Ended by AICoder, pid:7164bof7c0x863614a690ba900d42c4d6340f340 */

/* Started by AICoder, pid:b7222s4f97u5dff1442b0bae20d2bc2d0c36e245 */
/* 打包上传文件列表首发帧 */
int pack_filelist_first_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no)
{
    int i = 0;
    int offset = 0;
    int totalByte = 0;

    //获取文件列表
    get_record_file_list_new((char**)s_upload_trsdata_ctr_inf.tFileList.pucListName, (char*)s_upload_trsdata_ctr_inf.tFileList.aucListNameLen, 
                    &s_upload_trsdata_ctr_inf.tFileList.ucListTotalNum);

    //计算总帧数
    for (i=0; i<s_upload_trsdata_ctr_inf.tFileList.ucListTotalNum; i++)
    {
        totalByte  += rt_strnlen_s(s_upload_trsdata_ctr_inf.tFileList.pucListName[i], s_upload_trsdata_ctr_inf.tFileList.aucListNameLen[i]);
    }
    s_upload_trsdata_ctr_inf.tFileList.wListTotalFrame = (totalByte + UPLOAD_DATA_FRAME_LEN - 1) / UPLOAD_DATA_FRAME_LEN;

    put_int16_to_buff(&tran_cmd_buf->buf[offset], s_upload_trsdata_ctr_inf.tFileList.wListTotalFrame);

    offset += 2;
    tran_cmd_buf->data_len = offset;
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;

    return SUCCESSFUL;
}
/* Ended by AICoder, pid:b7222s4f97u5dff1442b0bae20d2bc2d0c36e245 */

/* Started by AICoder, pid:e80d0ee1dfsbaf714315091c10c5482c382163e6 */
/* 打包上传文件列表其他帧 */
int pack_filelist_other_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no)
{
    int i = 0;
    int offset = 0;
    //获取文件列表
    for (i=0; i<s_upload_trsdata_ctr_inf.tFileList.ucListTotalNum; i++)
    {
        put_int16_to_buff(&tran_cmd_buf->buf[offset], s_upload_trsdata_ctr_inf.tFileList.aucListNameLen[i]);
        offset += 2;

        rt_memcpy_s(&tran_cmd_buf->buf[offset], s_upload_trsdata_ctr_inf.tFileList.aucListNameLen[i], 
            s_upload_trsdata_ctr_inf.tFileList.pucListName[i], s_upload_trsdata_ctr_inf.tFileList.aucListNameLen[i]);
        offset += s_upload_trsdata_ctr_inf.tFileList.aucListNameLen[i];
    }
    
    tran_cmd_buf->data_len = offset;
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;

    return SUCCESSFUL;
}
/* Ended by AICoder, pid:e80d0ee1dfsbaf714315091c10c5482c382163e6 */

/* Started by AICoder, pid:d457ci2f2ac245d143f209aac0f1cd3263f25106 */
/* 解析上传文件数据帧 解析首发帧，其他帧DATA为空 */
int parse_upload_file_data(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = NULL;
    T_FileUploadStruct* trsdata_ctr_inf = NULL;
    download_cmd_head_t* proto_head = NULL;
    trsdata_ctr_inf = &s_upload_trsdata_ctr_inf;

    // 入参校验
    if ( cmd_buf == NULL || ((cmd_buf_t*)cmd_buf)->buf == NULL)
    {
        return FAILURE;
    }

    tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    //判断协议层解析数据是否正确
    if (tran_cmd_buf->rtn != DOWNLOAD_FRAME_CORRECT)
    {
        return FAILURE;
    }
    proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->req_head;
    trsdata_ctr_inf->cur_frame_no = proto_head->fn2;
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;
    
    if (trsdata_ctr_inf->cur_frame_no == 0)
    {
        trig_end();
        parse_filedata_first_frame(tran_cmd_buf, trsdata_ctr_inf->cur_frame_no);
    }

    return SUCCESSFUL;
}
/* Ended by AICoder, pid:d457ci2f2ac245d143f209aac0f1cd3263f25106 */

/* Started by AICoder, pid:s4175c6550g1a3c146440806a06ca91fe3e0e25b */
/* 解析上传文件数据首发帧 */
int parse_filedata_first_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no)
{
    s_upload_trsdata_ctr_inf.tFileName.wReqFileNameLen = get_int16_data(&tran_cmd_buf->buf[0]); //请求的文件名长度
    rt_memset_s( s_upload_trsdata_ctr_inf.tFileName.ucReqFileName , UPLOAD_FILE_NAME_MAX_LEN , 0 , UPLOAD_FILE_NAME_MAX_LEN );
    rt_memcpy_s(s_upload_trsdata_ctr_inf.tFileName.ucReqFileName, s_upload_trsdata_ctr_inf.tFileName.wReqFileNameLen, 
        &tran_cmd_buf->buf[2], s_upload_trsdata_ctr_inf.tFileName.wReqFileNameLen); //请求的文件名

    return SUCCESSFUL;
}
/* Ended by AICoder, pid:s4175c6550g1a3c146440806a06ca91fe3e0e25b */

/* Started by AICoder, pid:pc955sf0810ac701472b0ae4d0a1c53a2ca3a206 */
/* 打包上传文件数据帧 打包首发帧和其他帧 */
int pack_upload_file_data(void* dev_inst, void *cmd_buf)
{
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    unsigned short frm_no = 0;
    download_cmd_head_t* proto_head = NULL;  

    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL)
    {
        return FAILURE;
    }

    //判断协议层解析数据是否正确
    if (tran_cmd_buf->rtn != DOWNLOAD_FRAME_CORRECT)
    {
        return FAILURE;
    }

    proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->req_head;
    frm_no = proto_head->fn2;
    
    if (frm_no == 0)
    {
        pack_filedata_first_frame(tran_cmd_buf, frm_no);
    }
    else
    {
        pack_filedata_other_frame(tran_cmd_buf, frm_no);
    } 

    return SUCCESSFUL;
}
/* Ended by AICoder, pid:pc955sf0810ac701472b0ae4d0a1c53a2ca3a206 */

/* Started by AICoder, pid:9b1f3ibbb01f3fe141d10ab700f5bb49d3c3019c */
/* 打包上传文件数据首发帧 */
int pack_filedata_first_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no)
{
    int offset = 0;
    time_t pt_time;
    static unsigned int crc_ctx = 0 ^ 0xFFFFFFFF; // 校验和

    // 获取文件总长度，his_data.c提交后放开
    s_upload_trsdata_ctr_inf.uFileTotalLen = get_record_file_len_new(s_upload_trsdata_ctr_inf.tFileName.ucReqFileName);

    //总帧数
    s_upload_trsdata_ctr_inf.wFileTotalFrame = (s_upload_trsdata_ctr_inf.uFileTotalLen + UPLOAD_DATA_FRAME_LEN - 1) 
                                                / UPLOAD_DATA_FRAME_LEN; //向上取整
    put_int16_to_buff(&(tran_cmd_buf->buf[offset]), s_upload_trsdata_ctr_inf.wFileTotalFrame);
    offset += 2;

    //上传文件名
    rt_memcpy_s(&tran_cmd_buf->buf[offset], s_upload_trsdata_ctr_inf.tFileName.wReqFileNameLen, 
        s_upload_trsdata_ctr_inf.tFileName.ucReqFileName, s_upload_trsdata_ctr_inf.tFileName.wReqFileNameLen);
    offset += s_upload_trsdata_ctr_inf.tFileName.wReqFileNameLen;

    //上传文件总大小
    put_int32_to_buff(&(tran_cmd_buf->buf[offset]), s_upload_trsdata_ctr_inf.uFileTotalLen);
    offset += 4;

    //上传文件时间
    pt_time = get_timestamp_s();
    rt_memset_s(&s_upload_trsdata_ctr_inf.ucFileTime[0], UPLOAD_FILE_TIME_LEN, 0, UPLOAD_FILE_TIME_LEN);
    put_time_t_to_buff(s_upload_trsdata_ctr_inf.ucFileTime, pt_time);
    rt_memcpy_s(&tran_cmd_buf->buf[offset], sizeof(s_upload_trsdata_ctr_inf.ucFileTime), s_upload_trsdata_ctr_inf.ucFileTime, sizeof(s_upload_trsdata_ctr_inf.ucFileTime));
    offset += UPLOAD_FILE_TIME_LEN;

    //文件校验码
    crc32_calc(&crc_ctx, tran_cmd_buf->buf, offset);
    s_upload_trsdata_ctr_inf.uCrc = crc_ctx;
    put_int16_to_buff(&(tran_cmd_buf->buf[offset]), s_upload_trsdata_ctr_inf.uCrc);
    offset += 4;

    tran_cmd_buf->data_len = offset;
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;

    return SUCCESSFUL;
}
/* Ended by AICoder, pid:9b1f3ibbb01f3fe141d10ab700f5bb49d3c3019c */

/* Started by AICoder, pid:0c25fg870fda1cf14a3a09eec05d9221155116d9 */
/* 打包上传文件数据其他帧 */
int pack_filedata_other_frame(cmd_buf_t* tran_cmd_buf, unsigned short frm_no)
{
    int offset = 0;
    int actual_len = 0;
    int tmp = 0;

    //当请求的帧数等于总帧数+1时直接返回
    
    //求当前文件读取偏移
    offset = (frm_no - 1) * UPLOAD_DATA_FRAME_LEN;
    rt_memset_s(tran_cmd_buf->buf, BUFF_LEN_2048, 0, BUFF_LEN_2048);
    tmp = s_upload_trsdata_ctr_inf.uFileTotalLen - offset ;
    actual_len =( tmp >= UPLOAD_DATA_FRAME_LEN ) ? UPLOAD_DATA_FRAME_LEN : tmp ;
    //获取文件数据，his_data.c提交后放开
    actual_len = get_frame_from_record_file_new(s_upload_trsdata_ctr_inf.tFileName.ucReqFileName, offset, actual_len, tran_cmd_buf->buf);

    tran_cmd_buf->data_len = actual_len;
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;
    return SUCCESSFUL;
}
/* Ended by AICoder, pid:0c25fg870fda1cf14a3a09eec05d9221155116d9 */


/* Started by AICoder, pid:x500ble0fe3176114d6d08b2d04852034746b688 */
void transfer_timeout()
{   
    rt_timer_stop(s_transfer_timer);
    rt_kprintf("transfer interrupt\n");
    if(s_send_msg_to_led_cb != NULL){
        s_send_msg_to_led_cb(LED_BLINK_CTRL_MSG, MOD_LED, 0, 2);
    }
    update_status_save_event(EVENT_UPDATE_FAILURE);
    return;
}
/* Ended by AICoder, pid:x500ble0fe3176114d6d08b2d04852034746b688 */

/* Started by AICoder, pid:c23b4ud3dcvdc5b142320a2bb07f993a06551a63 */
/* 解析下载帧 */
int parse_update_data(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    unsigned short frm_no = 0;
    download_cmd_head_t* proto_head = NULL;
    update_file_manage_t* trsdata_ctr_inf = NULL;
    trsdata_ctr_inf = &s_download_trsdata_ctr_inf;
    trsdata_ctr_inf->rtn = 0;

    rt_timer_stop(s_transfer_timer);     
    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    //判断协议层解析数据是否正确
    if(tran_cmd_buf->rtn != DOWNLOAD_FRAME_CORRECT){
        return SUCCESSFUL;
    }
    proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->req_head;
    frm_no = proto_head->fn2;

    rt_kprintf("frame_no:%d\n", frm_no);

    // 首发帧处理
    RETURN_VAL_IF_FAIL(is_first_frame_deal(frm_no, tran_cmd_buf) == SUCCESSFUL, SUCCESSFUL);
    // 是否期望帧
    RETURN_VAL_IF_FAIL(is_expect_date_frame_no(frm_no, trsdata_ctr_inf, tran_cmd_buf) == SUCCESSFUL, SUCCESSFUL);
    // 第一帧处理(擦除flash)
    RETURN_VAL_IF_FAIL(erase_store_memory_in_first_frame(frm_no, &tran_cmd_buf->rtn) == SUCCESSFUL, SUCCESSFUL);
    g_first_frame_flag = FALSE;
    parse_data_frame(dev_inst, tran_cmd_buf, frm_no);
    tran_cmd_buf->rtn = trsdata_ctr_inf->rtn;
    return SUCCESSFUL;
}
/* Ended by AICoder, pid:c23b4ud3dcvdc5b142320a2bb07f993a06551a63 */

/* Started by AICoder, pid:ea4a01ef67h1545149580b7d90683c71e8409bce */
int parse_first_frame(cmd_buf_t* tran_cmd_buf,unsigned short frm_no){
    int update_first_max_len = 0;
    int update_file_name_len = 0;
    update_file_manage_t* trsdata_ctr_inf = &s_download_trsdata_ctr_inf;
    update_file_attr_t file_info = {0};                     

    //区分扩展帧和非扩展帧
    if(tran_cmd_buf->cmd->cmd_id == DOWNLOAD_DATA_DOWNLOAD_EXTEND){
        update_first_max_len = DOWNLOAD_UPDATE_FIRST_EXTEND_FRAME_LEN;
        update_file_name_len = UPDATE_FILE_EXTEND_NAME_LEN;
    }else{
        update_first_max_len = DOWNLOAD_UPDATE_FIRST_FRAME_LEN;
        update_file_name_len = UPDATE_FILE_NAME_LEN;
    }

    if(tran_cmd_buf->data_len != update_first_max_len) {
        trsdata_ctr_inf->rtn = DOWNLOAD_FIRST_FRAME_ERR;
        LOG_E("%s:%d|first_frame data_len:%d", __FUNCTION__ , __LINE__, tran_cmd_buf->data_len);
        return SUCCESSFUL;
    }

    // 总帧数
    file_info.total_frames = get_int16_data(&tran_cmd_buf->buf[0]);
    // 文件大小
    file_info.total_leng = get_ulong_data(&tran_cmd_buf->buf[2]);
    g_download_total_size = file_info.total_leng;
    // if(file_info.total_leng > APP_DOWNLOAD_SIZE)
    if(file_info.total_leng > 1.5*1024*1024)
    {
        LOG_E("%s:%d|first_frame total_leng:%d", __FUNCTION__ , __LINE__, file_info.total_leng);
        trsdata_ctr_inf->rtn = DOWNLOAD_FIRST_FRAME_ERR;
        return SUCCESSFUL;
    }
    // 文件名
    rt_memcpy_s(file_info.file_name, update_file_name_len, &tran_cmd_buf->buf[6], update_file_name_len);
    if(update_file_name_cmp(file_info.file_name) == FAILURE)
    {
        LOG_E("%s:%d|first_frame file_name:%d", __FUNCTION__ , __LINE__, file_info.file_name);
        trsdata_ctr_inf->rtn = DOWNLOAD_FIRST_FRAME_ERR;
        return SUCCESSFUL;
    }
    // 文件时间
    rt_memcpy_s(file_info.file_time, UPDATE_FILE_TIME_LEN, &tran_cmd_buf->buf[6+update_file_name_len], UPDATE_FILE_TIME_LEN);

    if(tran_cmd_buf->cmd->cmd_id == DOWNLOAD_DATA_DOWNLOAD_EXTEND){//区分扩展帧和正常帧
        // file_info.filecrc = get_ulong_data(&tran_cmd_buf->buf[6+update_file_name_len + UPDATE_FILE_TIME_LEN]);
        g_extend_file_crc = get_ulong_data(&tran_cmd_buf->buf[6+update_file_name_len + UPDATE_FILE_TIME_LEN]);
        file_info.param_type = tran_cmd_buf->buf[6+update_file_name_len + UPDATE_FILE_TIME_LEN +4];
    }else{
        file_info.param_type = tran_cmd_buf->buf[6+update_file_name_len + UPDATE_FILE_TIME_LEN];
        g_extend_file_crc = 0;
    }
    // 支持断点续传
    if (file_change_check(&trsdata_ctr_inf->file_info, &file_info) == TRUE) {
        trsdata_ctr_inf->file_info.filecrc = 0;
        rt_memset_s(&trsdata_ctr_inf->file_info, sizeof(update_file_attr_t), 0x00, sizeof(update_file_attr_t));
        rt_memcpy_s(&trsdata_ctr_inf->file_info, sizeof(update_file_attr_t), &file_info, sizeof(update_file_attr_t));
        trsdata_ctr_inf->data_offset = 0;
        trsdata_ctr_inf->cur_frame_no = frm_no + 1;
    }
    trsdata_ctr_inf->rtn = DOWNLOAD_FRAME_CORRECT;
    trsdata_ctr_inf->update_status = UPDATEING;

    rt_kprintf("first_frame|total_frames:%d, total_leng:%d, file_name:%s\n", 
        file_info.total_frames, file_info.total_leng, file_info.file_name);

    //断点续传数据(存放在芯片里面，不用额外增加flash分区)
    write_download_tmpInfo(trsdata_ctr_inf);
    return SUCCESSFUL;

}
/* Ended by AICoder, pid:ea4a01ef67h1545149580b7d90683c71e8409bce */

/* Started by AICoder, pid:i7b96y9edcm852114f110a553000501dafe695b5 */
void read_download_tmpInfo(update_file_manage_t* tFileManage) {
   part_data_t part_data = {0};

    if (NULL == tFileManage)
        return;
    
    part_data.buff = (unsigned char *)tFileManage;
    part_data.len = sizeof(update_file_manage_t);
    part_data.offset = s_cur_download_file_info.cur_store_info_offset;;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), s_cur_download_file_info.cur_store_info_name);
    if (SUCCESSFUL != storage_process(&part_data, read_opr)) {
        return ;
    }

    return ;
}
/* Ended by AICoder, pid:i7b96y9edcm852114f110a553000501dafe695b5 */

/* Started by AICoder, pid:q06f0d530akff6914af20bc57059182efda2c90f */
void write_download_tmpInfo(update_file_manage_t* tFileManage) {
    part_data_t part_data = {0};
    update_file_manage_t check_update_info = {0};

    if (NULL == tFileManage)
        return;
    tFileManage->crc = crc_cal((unsigned char *)tFileManage, sizeof(update_file_manage_t) - 2);
    part_data.buff = (unsigned char *)tFileManage;
    part_data.len = sizeof(update_file_manage_t);
    part_data.offset = s_cur_download_file_info.cur_store_info_offset;;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), s_cur_download_file_info.cur_store_info_name);
    if (SUCCESSFUL != storage_process(&part_data, erase_write_opr)) {
        return ;
    }

    //检验升级信息是否写成功
    read_download_tmpInfo(&check_update_info);
    if(check_update_info.crc != tFileManage->crc)
    {
        LOG_E("update Info error | write crc:%d read crc:%d",tFileManage->crc, check_update_info.crc);
    }
}
/* Ended by AICoder, pid:q06f0d530akff6914af20bc57059182efda2c90f */

/* Started by AICoder, pid:t13c74cadcq7ef41480d0bcc70f5cf4d36b6fc8e */
int parse_data_frame(void* dev_inst, cmd_buf_t* tran_cmd_buf, unsigned short frm_no)
{
    part_data_t part_data = {0};
    update_file_manage_t* trsdata_ctr_inf = &s_download_trsdata_ctr_inf;
    unsigned short crc = trsdata_ctr_inf->file_info.filecrc;
    
    //存储数据帧
    part_data.buff = tran_cmd_buf->buf;
    part_data.len = tran_cmd_buf->data_len;
    part_data.offset = trsdata_ctr_inf->data_offset;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), s_cur_download_file_info.cur_store_file_name);
    if (SUCCESSFUL != storage_process(&part_data, write_opr))
    {
        trsdata_ctr_inf->rtn = DOWNLOAD_FRAME_ERR;
        return SUCCESSFUL;
    }


    //计算校验和,判断升级文件是否有问题
    crc = crc16_calc_with_init_crc(tran_cmd_buf->buf, tran_cmd_buf->data_len, crc);

    //更新数据
    trsdata_ctr_inf->cur_frame_no = frm_no + 1;
    trsdata_ctr_inf->data_offset += tran_cmd_buf->data_len;
    trsdata_ctr_inf->rtn = DOWNLOAD_FRAME_CORRECT;
    trsdata_ctr_inf->file_info.filecrc = crc;
    if(frm_no % SAVE_UPDATE_DATA_INDEX == 0)
    {
        write_download_tmpInfo(trsdata_ctr_inf);
    }

    rt_timer_start(s_transfer_timer);

    if(frm_no == trsdata_ctr_inf->file_info.total_frames - 1)
    {
        // 判断是否是扩展帧
        if(g_extend_file_crc != 0)
        {
            trsdata_ctr_inf->file_info.filecrc = g_extend_file_crc;
        }
        LOG_E("update crc:%d\n",trsdata_ctr_inf->file_info.filecrc);
        deal_last_frame(frm_no, trsdata_ctr_inf, tran_cmd_buf, dev_inst);
    }

    return SUCCESSFUL;
}
/* Ended by AICoder, pid:t13c74cadcq7ef41480d0bcc70f5cf4d36b6fc8e */

/* Started by AICoder, pid:b5b3ec54834262b14cd20b43c0e26d2c5314543a */
int pack_update_data(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    download_cmd_head_t* proto_head = NULL;
    
    // 入参校验
    if (tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    tran_cmd_buf->data_len = 0;
    proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->ack_head;
    if(g_first_frame_flag == TRUE)
    {
        proto_head->fn1 = 0;
        proto_head->fn2 = s_download_trsdata_ctr_inf.cur_frame_no;
    }
    else
    {
        proto_head->fn1 = s_download_trsdata_ctr_inf.cur_frame_no - 1;
        proto_head->fn2 = s_download_trsdata_ctr_inf.cur_frame_no;
    }
    // rt_kprintf("pack_update_data|fn1:%d, fn2:%d\n", proto_head->fn1, proto_head->fn2);
    return SUCCESSFUL;
}
/* Ended by AICoder, pid:b5b3ec54834262b14cd20b43c0e26d2c5314543a */

/* Started by AICoder, pid:0db75ja245j5c8a147a50a8890f6a2223da989e5 */
int parse_update_trig(void* dev_inst, void *cmd_buf) {
    //开启触发定时器
    rt_timer_start(s_trig_timer);
    s_download_trig_ctr_inf.trig_success = FALSE;
    s_download_trig_ctr_inf.trig_times++;
    //触发成功
    if(s_download_trig_ctr_inf.trig_times >= DOWNLOAD_TRIG_TIMES){
        s_download_trig_ctr_inf.trig_success = TRUE;
        s_download_trig_ctr_inf.trig_times = 0;
        g_zk_update_flag = FALSE;
        if(s_send_msg_to_led_cb != NULL){
            s_send_msg_to_led_cb(LED_BLINK_CTRL_MSG, MOD_LED, 0, 3);
        }
    }
    return SUCCESSFUL;
}

int pack_update_trig(void* dev_inst, void *cmd_buf) {
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    download_cmd_head_t* proto_head = NULL;
    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL) {
        return FAILURE;
    }
    proto_head = (download_cmd_head_t*)tran_cmd_buf->cmd->ack_head;
    proto_head->cid = CMD_TRIG_FRAME_TYPE_H;
    proto_head->frame_type_l = CMD_TRIG_FRAME_TYPE_L;
    proto_head->chip_type_h = CMD_TRIG_CHIP_TYPE_H;
    proto_head->chip_type_l = CMD_TRIG_VERSION; 
    tran_cmd_buf->data_len = (unsigned int)sizeof(trig_frame_ack);
    rt_memcpy_s(tran_cmd_buf->buf, sizeof(trig_frame_ack), trig_frame_ack, sizeof(trig_frame_ack));
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;
    return SUCCESSFUL;
}
/* Ended by AICoder, pid:0db75ja245j5c8a147a50a8890f6a2223da989e5 */

int parse_ver_cmp(void* dev_inst, void *cmd_buf){
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    char ver_file_name_buff[VER_FILE_LEN] = {0};
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL) {
        return FAILURE;
    }
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;
    rt_memcpy_s(ver_file_name_buff, VER_FILE_NAME_LEN, tran_cmd_buf->buf, VER_FILE_NAME_LEN);
    if(update_file_name_cmp(ver_file_name_buff) != SUCCESSFUL){
        tran_cmd_buf->rtn = DOWNLOAD_FRAME_ERR;
    }
    return SUCCESSFUL;
}
int pack_ver_cmp(void* dev_inst, void *cmd_buf){
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    tran_cmd_buf->data_len = 0;
    return SUCCESSFUL;
}
int parse_stop_update(void* dev_inst, void *cmd_buf){
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    char ver_file_name_buff[VER_FILE_LEN] = {0};
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    rt_memcpy_s(ver_file_name_buff, VER_FILE_NAME_LEN, tran_cmd_buf->buf, VER_FILE_NAME_LEN);
    if(strcmp(s_download_trsdata_ctr_inf.file_info.file_name, ver_file_name_buff)){
       tran_cmd_buf->rtn = DOWNLOAD_FRAME_ERR;
    }else{
        tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;
        s_download_trsdata_ctr_inf.update_status = UPDATE_FAILURE;
        // 升级失败，记录操作日志
        update_status_save_event(EVENT_UPDATE_FAILURE);
    }
    rt_timer_stop(s_trig_timer);
    rt_timer_stop(s_transfer_timer);
    if(s_send_msg_to_led_cb != NULL){
        s_send_msg_to_led_cb(LED_BLINK_CTRL_MSG, MOD_LED, 0, 2);
    }
    return SUCCESSFUL;
}
int pack_stop_update(void* dev_inst, void *cmd_buf){
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    tran_cmd_buf->data_len = 0;
    return SUCCESSFUL;
}
int parse_update_progress(void* dev_inst, void *cmd_buf){
    return SUCCESSFUL;
}
int pack_update_progress(void* dev_inst, void *cmd_buf){
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    int offset = 0;
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;
    tran_cmd_buf->buf[offset++] = s_download_trsdata_ctr_inf.update_status;
    tran_cmd_buf->buf[offset++] = ((float)(s_download_trsdata_ctr_inf.cur_frame_no )*100)/(float)(s_download_trsdata_ctr_inf.file_info.total_frames);
    tran_cmd_buf->buf[offset++] = 0;
    tran_cmd_buf->data_len = offset;
    return SUCCESSFUL;
}
int parse_get_flag(void* dev_inst, void *cmd_buf){
    return SUCCESSFUL;
}
int pack_get_flag(void* dev_inst, void *cmd_buf){
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    rt_memset_s(tran_cmd_buf->buf,MAC_DATA_LEN,0,MAC_DATA_LEN);
    tran_cmd_buf->data_len = MAC_DATA_LEN;
    // get_mac_addr(tran_cmd_buf->buf, tran_cmd_buf->data_len);
    return SUCCESSFUL;
}
int parse_get_single_frame_max_length(void* dev_inst, void *cmd_buf){
    return SUCCESSFUL;
}
int pack_get_single_frame_max_length(void* dev_inst, void *cmd_buf){
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    unsigned short single_frame_max_length = 0;
    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL) {
        return FAILURE;
    }

    tran_cmd_buf->data_len = 0x02;
    single_frame_max_length = get_one_frame_max_len();
    put_int16_to_buff(tran_cmd_buf->buf, single_frame_max_length);
    tran_cmd_buf->rtn = DOWNLOAD_FRAME_CORRECT;
    return SUCCESSFUL;
}

int parse_set_single_frame_max_length(void* dev_inst, void *cmd_buf){
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    unsigned short single_frame_max_length = 0;
    // 入参校验
    if ( tran_cmd_buf == NULL || tran_cmd_buf->buf == NULL) {
        return FAILURE;
    }
    single_frame_max_length = (unsigned short)get_int16_data(tran_cmd_buf->buf);

    if(single_frame_max_length > SINGLE_FRAME_MAX_LENGTH_MAX){
        tran_cmd_buf->rtn = DOWNLOAD_FRAME_ERR;
    }
    set_one_frame_max_len(single_frame_max_length);
    return SUCCESSFUL;
}
int pack_set_single_frame_max_length(void* dev_inst, void *cmd_buf){
    cmd_buf_t* tran_cmd_buf = (cmd_buf_t*)cmd_buf;
    tran_cmd_buf->data_len = 0;
    return SUCCESSFUL;
}

/* Started by AICoder, pid:h478549e83ef9671422b09aee075941b5e70c629 */
int file_change_check(update_file_attr_t *pold_file, update_file_attr_t *pnew_file) {
    if ((rt_strcmp(pold_file->file_name, pnew_file->file_name) == 0) && \
        (rt_strcmp(pold_file->file_time, pnew_file->file_time) == 0) && \
        (pold_file->total_frames == pnew_file->total_frames) && \
        (pold_file->total_leng   == pnew_file->total_leng)) {
        return FALSE;
    } else {
        return TRUE;
    }
}
/* Ended by AICoder, pid:h478549e83ef9671422b09aee075941b5e70c629 */

/* Started by AICoder, pid:c7535l9481y38be149e70bc070c10828e0904041 */
int update_file_name_cmp(char* file_name)
{  
    for(int loop = 0; loop < MAX_DOWNLOAD_FILE_NUM; loop ++)
    {
        if(s_register_download_file_name[loop] == NULL || s_register_download_file_name[loop]->file_name == NULL)
        {
            continue;
        }
        if(rt_strcmp(s_register_download_file_name[loop]->file_name, file_name) == 0)
        {
             if(rt_strcmp(ZK_NAME, file_name ) == 0){
                g_zk_update_flag = TRUE;
            }
            s_cur_download_file_info.cur_file_type = s_register_download_file_name[loop]->file_type;
            s_cur_download_file_info.cur_file_name = s_register_download_file_name[loop]->file_name;
            s_cur_download_file_info.cur_store_file_name = s_register_download_file_name[loop]->store_name;
            LOG_E("file_type:%d, file_name:%s, store_name:%s\n", s_cur_download_file_info.cur_file_type, 
                s_cur_download_file_info.cur_file_name, s_cur_download_file_info.cur_store_file_name);
            return SUCCESSFUL;
        }
    }
    return FAILURE;
}
/* Ended by AICoder, pid:c7535l9481y38be149e70bc070c10828e0904041 */

/* Started by AICoder, pid:n5525f2454x46b11419b0a831085a82c80a1efe7 */
int update_download_init(void) {
    short i = 0;

    for(i = 0; i < sizeof(s_download_update_cmd_handle)/sizeof(s_download_update_cmd_handle[0]); i++) 
    {
        register_cmd_handle(&s_download_update_cmd_handle[i]);
    }

    s_trig_timer = rt_timer_create("trig_timer", trig_timeout, RT_NULL, 30000, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    if(s_trig_timer == NULL)
    {
        return FAILURE;
    }
    s_transfer_timer = rt_timer_create("transfer_timer", transfer_timeout, NULL, 30000, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
    if(s_transfer_timer == NULL)
    {
        return FAILURE;
    }

    return SUCCESSFUL;
}
/* Ended by AICoder, pid:n5525f2454x46b11419b0a831085a82c80a1efe7 */


/* Started by AICoder, pid:e505byd56bq68d214b6f0908401db81f1cb10b29 */
void trig_timeout()
{
    s_download_trig_ctr_inf.trig_times = 0;
    rt_timer_stop(s_trig_timer);
    if(s_send_msg_to_led_cb != NULL){
        s_send_msg_to_led_cb(LED_BLINK_CTRL_MSG, MOD_LED, 0, 2);
    }
}

void trig_end()
{
    s_download_trig_ctr_inf.trig_times = 0;
    rt_timer_stop(s_trig_timer);
}
/* Ended by AICoder, pid:e505byd56bq68d214b6f0908401db81f1cb10b29 */

/* Started by AICoder, pid:g0ae7afe968ca4714cad084b10d314310bd66b99 */
int is_first_frame_deal(unsigned short frm_no, cmd_buf_t* tran_cmd_buf)
{
    if (frm_no == 0)
    {
        trig_end();
        g_first_frame_flag = TRUE;
        rt_timer_start(s_transfer_timer);
        parse_first_frame(tran_cmd_buf, frm_no);

        return FAILURE;
    }
    return SUCCESSFUL;
}

int is_expect_date_frame_no(unsigned short frm_no, update_file_manage_t* trsdata_ctr_inf, cmd_buf_t* tran_cmd_buf)
{
    if (frm_no != trsdata_ctr_inf->cur_frame_no || trsdata_ctr_inf->update_status != UPDATEING)
    {
        tran_cmd_buf->rtn = DOWNLOAD_FRAME_ERR;
        return FAILURE;
    }
    return SUCCESSFUL;
}

int erase_store_memory_in_first_frame(unsigned short cur_frame_no, unsigned int* rtn)
{
    part_data_t part_data = {0};
    int ret = 0;
    rt_snprintf_s(part_data.name, sizeof(part_data.name), s_cur_download_file_info.cur_store_file_name);
    if(g_zk_update_flag && (1 == cur_frame_no)) {
        part_data.len = g_download_total_size;
        ret = storage_erase_with_step(&part_data, 1, s_cur_download_file_info.feed_dog);
        if (ret != SUCCESSFUL) {
            *rtn  = DOWNLOAD_FRAME_ERR;
            return FAILURE;
        }
    }
    else {
        if((cur_frame_no == 1) && (SUCCESSFUL != storage_process(&part_data, erase_opr)))
        {
            *rtn  = DOWNLOAD_FRAME_ERR;
            return FAILURE;
        }
    }

    return SUCCESSFUL;
}
/* Ended by AICoder, pid:g0ae7afe968ca4714cad084b10d314310bd66b99 */

/* Started by AICoder, pid:d07e7f786b229d914b730b12c0d95c474242795b */
void is_need_restart(void* dev_inst, cmd_buf_t *cmd_buf)
{
    update_file_manage_t tFileManage = {0};
    cmd_buf_t send_cmd_buff = {0};

    read_download_tmpInfo(&tFileManage);
    LOG_E("is_need_restart | tFileManage.update_flag: %d", tFileManage.update_flag);
    if(tFileManage.update_flag == FLAG_APP_UPDATE_APP)
    {   
        rt_kprintf("system soft reset");
        send_cmd_buff.cmd = cmd_buf->cmd;
        cmd_send(dev_inst, &send_cmd_buff);
        update_status_save_event(EVENT_UPDATE_SUCCESS);
        rt_thread_mdelay(5000);
        rt_hw_cpu_reset();
    }
    return ;
}


int self_update_deal(update_file_manage_t* trsdata_ctr_inf, cmd_buf_t *cmd_buf, void* dev_inst)
{   
    trsdata_ctr_inf->sys_run_flag = FALSE;
    trsdata_ctr_inf->backup_flag = FALSE;
    trsdata_ctr_inf->update_flag = FLAG_APP_UPDATE_APP;
    trsdata_ctr_inf->count = 0;

    if(g_zk_update_flag){
        trsdata_ctr_inf->ucFlag = FLAG_DOWNLOAD_ZK_IAP;
    }else{
        trsdata_ctr_inf->ucFlag = FLAG_DOWNLOAD_IAP;
    }
    write_download_tmpInfo(trsdata_ctr_inf);// 完成标记,文件传输完成

    is_need_restart(dev_inst, cmd_buf);   // 保证最后一包传输完成
    g_zk_update_flag = FALSE;
    return SUCCESSFUL;
}

void deal_last_frame(unsigned short frm_no, update_file_manage_t* trsdata_ctr_inf, cmd_buf_t *cmd_buf, void* dev_inst)
{
    trsdata_ctr_inf->update_status = TRANSFER_SUCCESS;
    rt_timer_stop(s_transfer_timer);
    // 还需要补充南向升级，其他文件下载
    if(s_cur_download_file_info.cur_file_type == MONITOR_FILE_TYPE)
    {
        self_update_deal(trsdata_ctr_inf, cmd_buf, dev_inst);
    }
    // 升级成功，记录操作日志
    update_status_save_event(EVENT_UPDATE_SUCCESS);
    g_zk_update_flag = FALSE;
    return;
}
/* Ended by AICoder, pid:d07e7f786b229d914b730b12c0d95c474242795b */


int update_status_save_event(update_result_save_event_t update_result_save_event_type){
    event_record_cb_param_t event_record_cb_param = {0};
    char info = update_result_save_event_type;

    switch(update_result_save_event_type){
        case EVENT_UPDATE_SUCCESS:
            if (g_zk_update_flag == TRUE) {
                event_record_cb_param.info[0] = ZK_DOWNLOAD_SUCCESS;
            } else {
                event_record_cb_param.info[0] = APP_DOWNLOAD_SUCCESS;
            }
            break;
        case EVENT_UPDATE_FAILURE:
            if (g_zk_update_flag == TRUE) {
                event_record_cb_param.info[0] = ZK_DOWNLOAD_FAIL;
            } else {
                event_record_cb_param.info[0] = APP_DOWNLOAD_FAIL;
            }
            break;
        default:
            return FAILURE;
    }

    event_record_cb_param.type = 0xFF;
    event_record_cb_param.info_len = 1;

    if(s_save_action_record_cb != NULL){
        s_save_action_record_cb(&event_record_cb_param);
    }
    return SUCCESSFUL;
}
