#ifndef _MQTTT_REALDATA_H_
#define _MQTTT_REALDATA_H_

#ifdef __cplusplus
extern "C" {
#endif    //  __cplusplus

#include "mqttclient.h"
#include "softbus.h"

#define GET_DEV_INFO(info, dev_idx)  ((info >> (dev_idx)) & 1)

void send_mqtt_realdata(void *parameter);
int create_mqtt_send_real_timer(void);

void send_master_property(mqtt_client_t* client);
int send_on_offline(mqtt_client_t* client, int dev_idx, int opt);
int handle_slave_property(void);
void handle_send_all_property_msg(_rt_msg_t curr_msg);
void handle_send_immediate_realdata_msg(_rt_msg_t curr_msg);
int judge_master_immediate_to_network();
int is_master_immediate_data_chg();
void handle_mqtt_realdata_msg(_rt_msg_t curr_msg);
void send_mqtt_realdata_msg(void *parameter);
void handle_mqtt_realdata_msg(_rt_msg_t curr_msg);
int handle_cur_slave_realdata_over(char dev_addr, char is_immediate_submit);
int update_cur_dev_info(unsigned char dev_addr);
#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif // _MQTTT_REALDATA_H_
