#include "msg.h"
#include "msg_id.h"
#include "sps.h"
#include "north_parallel_modbus.h"
#include "dev_dc_ac_1363.h"

#include "mqtt_utils.h"
#include "mqtt_set_get.h"
#include "para_common.h"

static cJSON* s_slave_json = NULL;
static mqtt_client_t* s_cur_client = NULL;
static unsigned int g_sid_list[PARA_SID_LIST_MAX] RAM_SECTION_BSS = {0};

int handle_set_data(void* client, message_data_t* msg)
{
    int id_num = 0;
    int set_idx = 0;
    int dev_idx = 0;
    int is_mst = TRUE;
    char topic[MQTT_TOPIC_LEN_MAX] = {0};
    char uuid[UUID_LEN] = {0};
    char site_id[SITE_ID_LEN] = {0};
    char device_id[STR_LEN_16] = {0};
    mqtt_err_e err = MQTT_ERR_MAX;
    cJSON* recv_arr = NULL;
    cJSON* recv_json = NULL;
    cJSON* send_arr = NULL;
    cJSON* send_json = cJSON_CreateObject();
    sid_list_info_t sid_list_info = {0};
    sid_list_info.sid_list = g_sid_list;
    sid_list_info.sid_num = 0;
    int which = 0;
    int south_rtn_flag = SUCCESSFUL;
    rt_memset(g_sid_list, 0x00, sizeof(g_sid_list));

    if (parse_msg_sn(msg->topic_name, &dev_idx) != MQTT_SUCCESS)
    {
        return MQTT_SCOPE_ERR;
    }

    if (dev_idx != (get_master_addr() - 1))
    {
        // 上一个从机消息未处理完不进行后续操作
        if (s_slave_json != NULL)
        {
            return MQTT_SUCCESS;
        }
        is_mst = FALSE;
    }

    which = which_client(client);
    get_site_id(which,site_id);
    get_device_id(dev_idx, device_id);
    rt_snprintf(topic, MQTT_TOPIC_LEN_MAX, "/zte/up%s/inv/%s/set/result", site_id, device_id);

    recv_json = mqtt_parse_msg(msg->message->payload, uuid);
    if (recv_json == NULL)
    {
        return MQTT_NULL_ERR;
    }

    recv_arr = cJSON_GetObjectItem(recv_json, PROPERTIES_OBJ);
    id_num = cJSON_GetArraySize(recv_arr);
    send_arr = cJSON_CreateArray();
    if (id_num < 1 || send_arr == NULL)
    {
        cJSON_Delete(recv_json);
        return MQTT_NULL_ERR;
    }

    for (set_idx = 0; set_idx < id_num; set_idx++)
    {
        cJSON* data = cJSON_GetArrayItem(recv_arr, set_idx);
        cJSON* item = cJSON_CreateObject();
        if (item == NULL)
        {
            continue;
        }
        cJSON* id = cJSON_GetObjectItem(data, "id");
        cJSON* val = cJSON_GetObjectItem(data, "value");
        if (id == NULL || val == NULL)
        {
            cJSON_Delete(item);
            continue;
        }
        cJSON_AddNumberToObject(item, "id", cJSON_GetNumberValue(id));
        err = mqtt_set_data(is_mst, dev_idx + 1, cJSON_GetNumberValue(id), val);

        // 收集参数sid
        collect_para_sids(&sid_list_info, cJSON_GetNumberValue(id), is_mst);
        if (get_slave_msg_cnt() == 0)
        {
            // 结束的时候设置下参数
            south_rtn_flag = send_set_para_cmd_msg(sid_list_info.sid_list, sid_list_info.sid_num);
            if(south_rtn_flag != SUCCESSFUL)
            {
                err = MQTT_REQ_TIMEOUT_ERR;
            }
        }
        cJSON_AddNumberToObject(item, "rtn", err);
        cJSON_AddItemToArray(send_arr, item);

    }

    cJSON_Delete(recv_json);

    cJSON_AddStringToObject(send_json, "msgId", uuid);
    cJSON_AddNumberToObject(send_json, "time", mqtt_get_timestamp());
    cJSON_AddItemToObject(send_json, RESULT_OBJ, send_arr);

    if (get_slave_msg_cnt() == 0)
    {
        send_mqtt_data(client, topic, send_json);
        cJSON_Delete(send_json);
    }
    else
    {
        s_slave_json = send_json;
        s_cur_client = client;
    }
    send_msg_to_thread(GRID_CODE_STATUS_CHECK, MOD_SYS_MANAGE, NULL, 0);
    if(south_rtn_flag != SUCCESSFUL)
    {
        return MQTT_REQ_TIMEOUT_ERR;
    }
    return MQTT_SUCCESS;
}

void handle_send_parallel_set_msg(_rt_msg_t curr_msg)
{
    char topic[MQTT_TOPIC_LEN_MAX] = {0};
    char site_id[SITE_ID_LEN] = {0};
    char device_id[STR_LEN_16] = {0};
    int which = 0;
    slave_ret_msg_t* send_msg_data = (slave_ret_msg_t*)curr_msg->msg.data;
    if (get_slave_msg_cnt() == 0)
    {
        return;
    }
    cJSON* arr = cJSON_GetObjectItem(s_slave_json, RESULT_OBJ);
    int arr_size = cJSON_GetArraySize(arr);

    which = which_client(s_cur_client);
    get_site_id(which, site_id);
    get_device_id(send_msg_data->addr - 1, device_id);
    rt_snprintf(topic, MQTT_TOPIC_LEN_MAX, "/zte/up%s/inv/%s/set/result", site_id, device_id);

    for (int i = 0; i < arr_size; i++)
    {
        cJSON* res = cJSON_GetArrayItem(arr, i);
        cJSON* id = cJSON_GetObjectItem(res, "id");
        if (check_control_id(cJSON_GetNumberValue(id), send_msg_data->id) != TRUE)
        {
            continue;
        }
        cJSON* rtn = cJSON_GetObjectItem(res, "rtn");
        cJSON_SetIntValue(rtn, send_msg_data->rtn);
    }

    set_slave_msg_cnt(get_slave_msg_cnt() - 1);
    if (get_slave_msg_cnt() == 0)
    {   
        send_mqtt_data(s_cur_client, topic, s_slave_json);
        cJSON_Delete(s_slave_json);
        s_slave_json = NULL;
    }
}

int is_get_slave_data_ok(char dev_addr)
{
    short wait_dev_cnt = 0;
    send_get_msg_to_slave(dev_addr, GET_SLAVE_PARA);

    while (is_slave_data_invalid(GET_SLAVE_PARA))
    {
        if (wait_dev_cnt++ > GET_SLAVE_DATA_TIMEOUT_CNT)
        {
            LOG_E("get slave %d para failed\n", dev_addr);
            return FALSE;
        }
        rt_thread_mdelay(10);
    }
    return TRUE;
}

cJSON* handle_get_data_arr(int is_mst, char dev_addr, cJSON* recv_arr, int id_num)
{
    int idx = 0;
    int ret = TRUE;
    cJSON* id = NULL;
    cJSON* send_arr = cJSON_CreateArray();

    RETURN_VAL_IF_FAIL(send_arr != NULL, NULL);

    if (!is_mst)
    {
        ret = is_get_slave_data_ok(dev_addr);
    }

    for (idx = 0; idx < id_num; idx++)
    {
        id = cJSON_GetArrayItem(recv_arr, idx);
        if (id == NULL)
        {
            continue;
        }
        cJSON* item = cJSON_CreateObject();
        if (item == NULL)
        {
            continue;
        }
        cJSON_AddNumberToObject(item, "id", cJSON_GetNumberValue(id));
        if (ret == FALSE)
        {
            cJSON_AddNumberToObject(item, "rtn", MQTT_NULL_ERR);
        }
        else
        {
            mqtt_err_e err = mqtt_get_data(is_mst, dev_addr, cJSON_GetNumberValue(id), item);
            cJSON_AddNumberToObject(item, "rtn", err);
        }
        cJSON_AddItemToArray(send_arr, item);
    }

    return send_arr;
}

int handle_get_data(void* client, message_data_t* msg)
{
    int id_num = 0;
    int dev_idx = 0;
    int is_mst = TRUE;
    char topic[MQTT_TOPIC_LEN_MAX] = {0};
    char uuid[UUID_LEN] = {0};
    char site_id[SITE_ID_LEN] = {0};
    char device_id[STR_LEN_16] = {0};
    int which = 0;

    cJSON* recv_arr = NULL;
    cJSON* recv_json = NULL;
    cJSON* send_arr = NULL;
    cJSON* time = NULL;

    if (parse_msg_sn(msg->topic_name, &dev_idx) != MQTT_SUCCESS)
    {
        return MQTT_SCOPE_ERR;
    }

    if (dev_idx != (get_master_addr() - 1))
    {
        is_mst = FALSE;
    }

    which = which_client(client);
    get_site_id(which, site_id);
    get_device_id(dev_idx, device_id);
    rt_snprintf(topic, MQTT_TOPIC_LEN_MAX, "/zte/up%s/inv/%s/get/result", site_id, device_id);

    recv_json = mqtt_parse_msg(msg->message->payload, uuid);
    if (recv_json == NULL)
    {
        return MQTT_NULL_ERR;
    }

    recv_arr = cJSON_GetObjectItem(recv_json, PROPERTIES_OBJ);
    id_num = cJSON_GetArraySize(recv_arr);
    if (id_num < 1)
    {
        cJSON_Delete(recv_json);
        return MQTT_NULL_ERR;
    }

    send_arr = handle_get_data_arr(is_mst, dev_idx + 1, recv_arr, id_num);

    cJSON_DeleteItemFromObject(recv_json, PROPERTIES_OBJ);
    time = cJSON_GetObjectItem(recv_json, "time");
    cJSON_SetNumberValue(time, mqtt_get_timestamp());
    cJSON_AddItemToObject(recv_json, RESULT_OBJ, send_arr);
    send_mqtt_data(client, topic, recv_json);
    cJSON_Delete(recv_json);
    return MQTT_SUCCESS;
}
